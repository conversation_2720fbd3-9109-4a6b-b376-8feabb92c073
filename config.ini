[DATABASE]
# SQL Server Configuration
server = localhost
database = ArchiveDB
driver = ODBC Driver 17 for SQL Server
trusted_connection = yes
connection_timeout = 30
command_timeout = 30

[APPLICATION]
# Application Settings
app_name = Electronic Archive System
version = 1.0.0
language = ar
theme = light
window_width = 1200
window_height = 800
max_file_size = 100MB
supported_formats = pdf,doc,docx,xls,xlsx,ppt,pptx,txt,jpg,jpeg,png,tiff,bmp

[STORAGE]
# File Storage Configuration
documents_path = ./documents
thumbnails_path = ./thumbnails
temp_path = ./temp
backup_path = ./backup
max_storage_size = 10GB

[SECURITY]
# Security Settings
encryption_enabled = true
password_policy = strong
session_timeout = 30
audit_enabled = true
backup_encryption = true

[SCANNING]
# Scanner Configuration
default_dpi = 300
default_format = pdf
auto_crop = true
auto_rotate = true
ocr_enabled = true
ocr_language = ara+eng

[SEARCH]
# Search Configuration
index_path = ./search_index
max_results = 100
highlight_results = true
fuzzy_search = true

[LOGGING]
# Logging Configuration
log_level = INFO
log_file = ./logs/archive.log
max_log_size = 10MB
backup_count = 5
