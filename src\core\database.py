"""
Database Manager for Electronic Archive System
مدير قاعدة البيانات لنظام الأرشفة الإلكترونية

Handles SQL Server database connections and operations using SQLAlchemy
"""

import logging
from typing import Optional, Dict, Any
from sqlalchemy import create_engine, text, MetaData
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import SQLAlchemyError
import pyodbc
from urllib.parse import quote_plus

from .config import Config


class DatabaseManager:
    """مدير قاعدة البيانات"""
    
    def __init__(self, config: Config):
        """
        Initialize database manager
        
        Args:
            config: Configuration manager instance
        """
        self.config = config
        self.engine = None
        self.SessionLocal = None
        self.metadata = MetaData()
        self.logger = logging.getLogger(__name__)
    
    def _build_connection_string(self) -> str:
        """بناء سلسلة الاتصال بقاعدة البيانات"""
        db_config = self.config.database_config
        
        # Build connection string for SQL Server
        if db_config['trusted_connection'].lower() == 'yes':
            # Windows Authentication
            connection_string = (
                f"mssql+pyodbc://@{db_config['server']}/{db_config['database']}"
                f"?driver={quote_plus(db_config['driver'])}"
                f"&trusted_connection=yes"
            )
        else:
            # SQL Server Authentication (if username/password provided)
            username = self.config.get('DATABASE', 'username', '')
            password = self.config.get('DATABASE', 'password', '')
            
            if username and password:
                connection_string = (
                    f"mssql+pyodbc://{username}:{password}@{db_config['server']}"
                    f"/{db_config['database']}"
                    f"?driver={quote_plus(db_config['driver'])}"
                )
            else:
                raise ValueError("اسم المستخدم وكلمة المرور مطلوبان للمصادقة")
        
        return connection_string
    
    def initialize(self) -> bool:
        """
        Initialize database connection and create tables if needed
        
        Returns:
            True if successful, False otherwise
        """
        try:
            # Build connection string
            connection_string = self._build_connection_string()
            
            # Create engine
            self.engine = create_engine(
                connection_string,
                pool_pre_ping=True,
                pool_recycle=3600,
                echo=False  # Set to True for SQL debugging
            )
            
            # Test connection
            with self.engine.connect() as conn:
                result = conn.execute(text("SELECT 1"))
                result.fetchone()
            
            # Create session factory
            self.SessionLocal = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.engine
            )
            
            # Create database schema if needed
            self._create_database_schema()
            
            self.logger.info("تم تهيئة قاعدة البيانات بنجاح")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في تهيئة قاعدة البيانات: {e}")
            return False
    
    def _create_database_schema(self):
        """إنشاء مخطط قاعدة البيانات"""
        try:
            with self.engine.connect() as conn:
                # Create tables using raw SQL for now
                # Later we'll use SQLAlchemy models
                
                # Users table
                conn.execute(text("""
                    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='users' AND xtype='U')
                    CREATE TABLE users (
                        id INT IDENTITY(1,1) PRIMARY KEY,
                        username NVARCHAR(50) UNIQUE NOT NULL,
                        email NVARCHAR(100) UNIQUE NOT NULL,
                        password_hash NVARCHAR(255) NOT NULL,
                        full_name NVARCHAR(100),
                        role NVARCHAR(20) DEFAULT 'user',
                        is_active BIT DEFAULT 1,
                        created_at DATETIME2 DEFAULT GETDATE(),
                        updated_at DATETIME2 DEFAULT GETDATE()
                    )
                """))
                
                # Categories table
                conn.execute(text("""
                    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='categories' AND xtype='U')
                    CREATE TABLE categories (
                        id INT IDENTITY(1,1) PRIMARY KEY,
                        name NVARCHAR(100) NOT NULL,
                        description NVARCHAR(500),
                        parent_id INT NULL,
                        color NVARCHAR(7) DEFAULT '#0078D4',
                        is_active BIT DEFAULT 1,
                        created_at DATETIME2 DEFAULT GETDATE(),
                        FOREIGN KEY (parent_id) REFERENCES categories(id)
                    )
                """))
                
                # Documents table
                conn.execute(text("""
                    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='documents' AND xtype='U')
                    CREATE TABLE documents (
                        id INT IDENTITY(1,1) PRIMARY KEY,
                        title NVARCHAR(255) NOT NULL,
                        description NVARCHAR(MAX),
                        file_name NVARCHAR(255) NOT NULL,
                        file_path NVARCHAR(500) NOT NULL,
                        file_size BIGINT NOT NULL,
                        file_type NVARCHAR(50) NOT NULL,
                        mime_type NVARCHAR(100),
                        category_id INT,
                        created_by INT NOT NULL,
                        document_date DATE,
                        tags NVARCHAR(500),
                        is_encrypted BIT DEFAULT 0,
                        thumbnail_path NVARCHAR(500),
                        full_text_content NVARCHAR(MAX),
                        created_at DATETIME2 DEFAULT GETDATE(),
                        updated_at DATETIME2 DEFAULT GETDATE(),
                        FOREIGN KEY (category_id) REFERENCES categories(id),
                        FOREIGN KEY (created_by) REFERENCES users(id)
                    )
                """))
                
                # Audit log table
                conn.execute(text("""
                    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='audit_log' AND xtype='U')
                    CREATE TABLE audit_log (
                        id INT IDENTITY(1,1) PRIMARY KEY,
                        user_id INT NOT NULL,
                        action NVARCHAR(50) NOT NULL,
                        table_name NVARCHAR(50),
                        record_id INT,
                        old_values NVARCHAR(MAX),
                        new_values NVARCHAR(MAX),
                        ip_address NVARCHAR(45),
                        user_agent NVARCHAR(500),
                        created_at DATETIME2 DEFAULT GETDATE(),
                        FOREIGN KEY (user_id) REFERENCES users(id)
                    )
                """))
                
                # Create indexes for better performance
                conn.execute(text("""
                    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name='IX_documents_title')
                    CREATE INDEX IX_documents_title ON documents(title)
                """))
                
                conn.execute(text("""
                    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name='IX_documents_created_at')
                    CREATE INDEX IX_documents_created_at ON documents(created_at)
                """))
                
                conn.execute(text("""
                    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name='IX_documents_category_id')
                    CREATE INDEX IX_documents_category_id ON documents(category_id)
                """))
                
                # Create full-text catalog and index for search
                conn.execute(text("""
                    IF NOT EXISTS (SELECT * FROM sys.fulltext_catalogs WHERE name='ArchiveCatalog')
                    CREATE FULLTEXT CATALOG ArchiveCatalog
                """))
                
                conn.execute(text("""
                    IF NOT EXISTS (SELECT * FROM sys.fulltext_indexes WHERE object_id = OBJECT_ID('documents'))
                    CREATE FULLTEXT INDEX ON documents(title, description, full_text_content)
                    KEY INDEX PK__documents__3213E83F
                    ON ArchiveCatalog
                """))
                
                conn.commit()
                
            self.logger.info("تم إنشاء مخطط قاعدة البيانات بنجاح")
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء مخطط قاعدة البيانات: {e}")
            raise
    
    def get_session(self) -> Session:
        """
        Get database session
        
        Returns:
            SQLAlchemy session
        """
        if not self.SessionLocal:
            raise RuntimeError("قاعدة البيانات غير مهيئة")
        
        return self.SessionLocal()
    
    def test_connection(self) -> bool:
        """
        Test database connection
        
        Returns:
            True if connection is successful
        """
        try:
            with self.engine.connect() as conn:
                result = conn.execute(text("SELECT 1"))
                result.fetchone()
            return True
        except Exception as e:
            self.logger.error(f"خطأ في اختبار الاتصال: {e}")
            return False
    
    def close(self):
        """إغلاق اتصال قاعدة البيانات"""
        if self.engine:
            self.engine.dispose()
            self.logger.info("تم إغلاق اتصال قاعدة البيانات")
