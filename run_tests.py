#!/usr/bin/env python3
"""
Test Runner for Electronic Archive System
مشغل الاختبارات لنظام الأرشفة الإلكترونية

Runs all tests and provides test results
"""

import sys
import subprocess
from pathlib import Path

def run_tests():
    """تشغيل جميع الاختبارات"""
    print("=" * 60)
    print("تشغيل اختبارات نظام الأرشفة الإلكترونية")
    print("Running Electronic Archive System Tests")
    print("=" * 60)
    
    # Check if pytest is installed
    try:
        import pytest
    except ImportError:
        print("pytest غير مثبت. يرجى تثبيته باستخدام:")
        print("pip install pytest")
        return False
    
    # Run tests
    test_args = [
        "tests/",
        "-v",  # Verbose output
        "--tb=short",  # Short traceback format
        "-x",  # Stop on first failure
    ]
    
    try:
        result = pytest.main(test_args)
        
        if result == 0:
            print("\n" + "=" * 60)
            print("جميع الاختبارات نجحت!")
            print("All tests passed!")
            print("=" * 60)
            return True
        else:
            print("\n" + "=" * 60)
            print("بعض الاختبارات فشلت")
            print("Some tests failed")
            print("=" * 60)
            return False
            
    except Exception as e:
        print(f"خطأ في تشغيل الاختبارات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    success = run_tests()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
