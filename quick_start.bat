@echo off
REM Quick Start for Electronic Archive System
REM بدء سريع لنظام الأرشفة الإلكترونية

echo ========================================
echo Electronic Archive System - Quick Start
echo نظام الأرشفة الإلكترونية - بدء سريع
echo ========================================

echo.
echo Installing core dependencies...
echo تثبيت المتطلبات الأساسية...
echo.

REM Install core dependencies
python -m pip install PySide6 sqlalchemy bcrypt python-dateutil

echo.
echo Starting demo application...
echo بدء تشغيل التطبيق التجريبي...
echo.

REM Start demo application
python demo_app.py

echo.
echo Demo completed!
echo انتهى العرض التجريبي!
echo.
echo Available applications:
echo التطبيقات المتاحة:
echo 1. python demo_app.py (Demo - تجريبي)
echo 2. python test_app.py (Test - اختبار)
echo 3. python main_simple.py (Simple - مبسط)
echo 4. python main.py (Full - كامل)
echo.
echo See START_HERE.md for detailed instructions
echo راجع ملف START_HERE.md للتعليمات المفصلة
echo.

pause
