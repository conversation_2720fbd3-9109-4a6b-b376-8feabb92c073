#!/usr/bin/env python3
"""
Quick Archive System - Simplified Launch
نظام الأرشفة السريع - تشغيل مبسط

A simplified launcher for the Electronic Archive System
"""

import sys
import subprocess
from pathlib import Path

def check_requirements():
    """فحص المتطلبات الأساسية"""
    print("🔍 فحص المتطلبات الأساسية...")
    
    required_packages = [
        ('PySide6', 'PySide6'),
        ('sqlalchemy', 'SQLAlchemy'),
        ('bcrypt', 'bcrypt'),
        ('PIL', 'Pillow')
    ]
    
    missing_packages = []
    
    for package_name, install_name in required_packages:
        try:
            __import__(package_name)
            print(f"✅ {install_name}")
        except ImportError:
            print(f"❌ {install_name} - مفقود")
            missing_packages.append(install_name)
    
    return missing_packages

def install_requirements(packages):
    """تثبيت المتطلبات المفقودة"""
    if not packages:
        return True
    
    print(f"\n📦 تثبيت المتطلبات المفقودة: {', '.join(packages)}")
    
    try:
        cmd = [sys.executable, "-m", "pip", "install"] + packages
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ تم تثبيت المتطلبات بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت المتطلبات: {e}")
        return False

def run_application():
    """تشغيل التطبيق"""
    print("\n🚀 تشغيل نظام الأرشفة الإلكترونية...")
    
    # Try different application versions
    app_files = [
        ("archive_system.py", "النظام المتكامل"),
        ("advanced_app.py", "النظام المتقدم"),
        ("main_simple.py", "النظام المبسط"),
        ("demo_app.py", "النظام التجريبي"),
        ("test_app.py", "تطبيق الاختبار")
    ]
    
    for app_file, description in app_files:
        if Path(app_file).exists():
            print(f"✅ تم العثور على: {description}")
            try:
                subprocess.run([sys.executable, app_file], check=True)
                return True
            except subprocess.CalledProcessError:
                print(f"❌ فشل في تشغيل: {description}")
                continue
            except KeyboardInterrupt:
                print("\n⏹️ تم إيقاف التطبيق بواسطة المستخدم")
                return True
    
    print("❌ لم يتم العثور على أي تطبيق قابل للتشغيل")
    return False

def show_menu():
    """عرض قائمة الخيارات"""
    print("=" * 60)
    print("نظام الأرشفة الإلكترونية - تشغيل سريع")
    print("Electronic Archive System - Quick Launch")
    print("=" * 60)
    
    print("\nالتطبيقات المتاحة:")
    print("Available Applications:")
    
    apps = [
        ("archive_system.py", "1", "النظام المتكامل - Complete System"),
        ("advanced_app.py", "2", "النظام المتقدم - Advanced System"),
        ("main_simple.py", "3", "النظام المبسط - Simple System"),
        ("demo_app.py", "4", "النظام التجريبي - Demo System"),
        ("test_app.py", "5", "تطبيق الاختبار - Test Application")
    ]
    
    available_apps = []
    for app_file, number, description in apps:
        if Path(app_file).exists():
            print(f"{number}. {description} ✅")
            available_apps.append((number, app_file, description))
        else:
            print(f"{number}. {description} ❌")
    
    print("\nخيارات إضافية:")
    print("Additional Options:")
    print("i. تثبيت المتطلبات - Install Requirements")
    print("q. خروج - Quit")
    
    return available_apps

def run_specific_app(app_file, description):
    """تشغيل تطبيق محدد"""
    print(f"\n🚀 تشغيل: {description}")
    try:
        subprocess.run([sys.executable, app_file])
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تشغيل التطبيق: {e}")
        return False
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التطبيق بواسطة المستخدم")
        return True

def main():
    """الدالة الرئيسية"""
    while True:
        available_apps = show_menu()
        
        if not available_apps:
            print("\n❌ لا توجد تطبيقات متاحة للتشغيل")
            print("يرجى التأكد من وجود ملفات التطبيق في المجلد الحالي")
            break
        
        print(f"\nاختر رقم التطبيق (1-5) أو خيار إضافي:")
        choice = input("الاختيار: ").strip().lower()
        
        if choice == 'q':
            print("👋 وداعاً!")
            break
        elif choice == 'i':
            print("\n📦 فحص وتثبيت المتطلبات...")
            missing = check_requirements()
            if missing:
                if install_requirements(missing):
                    print("✅ تم تثبيت جميع المتطلبات")
                else:
                    print("❌ فشل في تثبيت بعض المتطلبات")
            else:
                print("✅ جميع المتطلبات مثبتة بالفعل")
            
            input("\nاضغط Enter للمتابعة...")
            continue
        
        # Check if choice is a valid app number
        app_found = False
        for number, app_file, description in available_apps:
            if choice == number:
                run_specific_app(app_file, description)
                app_found = True
                break
        
        if not app_found:
            print("❌ اختيار غير صحيح، يرجى المحاولة مرة أخرى")
            input("اضغط Enter للمتابعة...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 تم إنهاء البرنامج")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")
