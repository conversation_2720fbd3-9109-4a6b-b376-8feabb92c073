"""
Audit Log Model for Electronic Archive System
نموذج سجل التدقيق لنظام الأرشفة الإلكترونية

Tracks all user actions and system events for security and compliance
"""

from datetime import datetime
from typing import Optional, Dict, Any, List
from sqlalchemy import Column, String, Integer, Text, ForeignKey, DateTime
from sqlalchemy.orm import relationship, Session
import json

from .base import BaseModel


class AuditLog(BaseModel):
    """
    Audit log model for tracking user actions and system events
    نموذج سجل التدقيق لتتبع إجراءات المستخدم وأحداث النظام
    """
    __tablename__ = "audit_log"
    
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False, index=True)
    action = Column(String(50), nullable=False, index=True)
    table_name = Column(String(50), nullable=True, index=True)
    record_id = Column(Integer, nullable=True, index=True)
    old_values = Column(Text, nullable=True)  # JSON string
    new_values = Column(Text, nullable=True)  # JSON string
    ip_address = Column(String(45), nullable=True)  # IPv6 compatible
    user_agent = Column(String(500), nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="audit_logs")
    
    # Override created_at to not use updated_at (audit logs are immutable)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    
    def __init__(self, **kwargs):
        # Remove updated_at and is_active from audit logs
        kwargs.pop('updated_at', None)
        kwargs.pop('is_active', None)
        super().__init__(**kwargs)
    
    @property
    def old_values_dict(self) -> Optional[Dict[str, Any]]:
        """
        Get old values as dictionary
        
        Returns:
            Dictionary of old values or None
        """
        if self.old_values:
            try:
                return json.loads(self.old_values)
            except json.JSONDecodeError:
                return None
        return None
    
    @old_values_dict.setter
    def old_values_dict(self, values: Optional[Dict[str, Any]]):
        """
        Set old values from dictionary
        
        Args:
            values: Dictionary of old values
        """
        self.old_values = json.dumps(values, ensure_ascii=False) if values else None
    
    @property
    def new_values_dict(self) -> Optional[Dict[str, Any]]:
        """
        Get new values as dictionary
        
        Returns:
            Dictionary of new values or None
        """
        if self.new_values:
            try:
                return json.loads(self.new_values)
            except json.JSONDecodeError:
                return None
        return None
    
    @new_values_dict.setter
    def new_values_dict(self, values: Optional[Dict[str, Any]]):
        """
        Set new values from dictionary
        
        Args:
            values: Dictionary of new values
        """
        self.new_values = json.dumps(values, ensure_ascii=False) if values else None
    
    @property
    def changes(self) -> Dict[str, Dict[str, Any]]:
        """
        Get changes between old and new values
        
        Returns:
            Dictionary of changes with 'old' and 'new' keys
        """
        changes = {}
        old_vals = self.old_values_dict or {}
        new_vals = self.new_values_dict or {}
        
        # Find all changed fields
        all_fields = set(old_vals.keys()) | set(new_vals.keys())
        
        for field in all_fields:
            old_val = old_vals.get(field)
            new_val = new_vals.get(field)
            
            if old_val != new_val:
                changes[field] = {
                    'old': old_val,
                    'new': new_val
                }
        
        return changes
    
    @classmethod
    def log_action(cls, session: Session, user_id: int, action: str,
                   table_name: Optional[str] = None, record_id: Optional[int] = None,
                   old_values: Optional[Dict[str, Any]] = None,
                   new_values: Optional[Dict[str, Any]] = None,
                   ip_address: Optional[str] = None,
                   user_agent: Optional[str] = None) -> 'AuditLog':
        """
        Log user action
        
        Args:
            session: Database session
            user_id: User ID performing the action
            action: Action performed
            table_name: Database table affected
            record_id: Record ID affected
            old_values: Old values before change
            new_values: New values after change
            ip_address: User's IP address
            user_agent: User's browser/client info
            
        Returns:
            Created audit log entry
        """
        audit_log = cls(
            user_id=user_id,
            action=action,
            table_name=table_name,
            record_id=record_id,
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        if old_values:
            audit_log.old_values_dict = old_values
        if new_values:
            audit_log.new_values_dict = new_values
        
        session.add(audit_log)
        session.commit()
        session.refresh(audit_log)
        
        return audit_log
    
    @classmethod
    def log_login(cls, session: Session, user_id: int, success: bool,
                  ip_address: Optional[str] = None, user_agent: Optional[str] = None) -> 'AuditLog':
        """
        Log login attempt
        
        Args:
            session: Database session
            user_id: User ID
            success: Whether login was successful
            ip_address: User's IP address
            user_agent: User's browser/client info
            
        Returns:
            Created audit log entry
        """
        action = "login_success" if success else "login_failed"
        return cls.log_action(
            session=session,
            user_id=user_id,
            action=action,
            ip_address=ip_address,
            user_agent=user_agent
        )
    
    @classmethod
    def log_document_action(cls, session: Session, user_id: int, action: str,
                           document_id: int, document_title: str = "",
                           ip_address: Optional[str] = None,
                           user_agent: Optional[str] = None) -> 'AuditLog':
        """
        Log document-related action
        
        Args:
            session: Database session
            user_id: User ID
            action: Action performed (create, update, delete, view, download)
            document_id: Document ID
            document_title: Document title for context
            ip_address: User's IP address
            user_agent: User's browser/client info
            
        Returns:
            Created audit log entry
        """
        return cls.log_action(
            session=session,
            user_id=user_id,
            action=f"document_{action}",
            table_name="documents",
            record_id=document_id,
            new_values={"document_title": document_title} if document_title else None,
            ip_address=ip_address,
            user_agent=user_agent
        )
    
    @classmethod
    def get_user_activity(cls, session: Session, user_id: int,
                         limit: int = 50, action_filter: Optional[str] = None) -> List['AuditLog']:
        """
        Get user activity log
        
        Args:
            session: Database session
            user_id: User ID
            limit: Maximum number of entries
            action_filter: Filter by action type
            
        Returns:
            List of audit log entries
        """
        query = session.query(cls).filter(cls.user_id == user_id)
        
        if action_filter:
            query = query.filter(cls.action.contains(action_filter))
        
        return query.order_by(cls.created_at.desc()).limit(limit).all()
    
    @classmethod
    def get_document_history(cls, session: Session, document_id: int) -> List['AuditLog']:
        """
        Get document change history
        
        Args:
            session: Database session
            document_id: Document ID
            
        Returns:
            List of audit log entries for the document
        """
        return session.query(cls).filter(
            cls.table_name == "documents",
            cls.record_id == document_id
        ).order_by(cls.created_at.desc()).all()
    
    @classmethod
    def get_recent_activity(cls, session: Session, limit: int = 100,
                           action_filter: Optional[str] = None) -> List['AuditLog']:
        """
        Get recent system activity
        
        Args:
            session: Database session
            limit: Maximum number of entries
            action_filter: Filter by action type
            
        Returns:
            List of recent audit log entries
        """
        query = session.query(cls)
        
        if action_filter:
            query = query.filter(cls.action.contains(action_filter))
        
        return query.order_by(cls.created_at.desc()).limit(limit).all()
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert audit log to dictionary
        
        Returns:
            Dictionary representation
        """
        data = {
            'id': self.id,
            'user_id': self.user_id,
            'action': self.action,
            'table_name': self.table_name,
            'record_id': self.record_id,
            'old_values': self.old_values_dict,
            'new_values': self.new_values_dict,
            'changes': self.changes,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
        
        return data
    
    def __repr__(self):
        return f"<AuditLog(id={self.id}, user_id={self.user_id}, action='{self.action}')>"
