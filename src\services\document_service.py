"""
Document Service for Electronic Archive System
خدمة الوثائق لنظام الأرشفة الإلكترونية

Handles document management, processing, and operations
"""

import logging
from typing import Optional, List, Dict, Any, Tuple
from pathlib import Path
from datetime import datetime, date

from ..core.config import Config
from ..core.database import DatabaseManager
from ..models import Document, Category, User, AuditLog
from .file_service import FileService


class DocumentService:
    """
    Service for document management and operations
    خدمة إدارة الوثائق والعمليات
    """
    
    def __init__(self, config: Config, db_manager: DatabaseManager):
        """
        Initialize document service
        
        Args:
            config: Configuration manager
            db_manager: Database manager
        """
        self.config = config
        self.db_manager = db_manager
        self.file_service = FileService(config, db_manager)
        self.logger = logging.getLogger(__name__)
    
    def create_document(self, file_path: Path, title: str, description: str = "",
                       category_id: Optional[int] = None, document_date: Optional[date] = None,
                       tags: Optional[List[str]] = None, created_by: int = None) -> Tuple[bool, str, Optional[Document]]:
        """
        Create new document from file
        
        Args:
            file_path: Path to source file
            title: Document title
            description: Document description
            category_id: Category ID
            document_date: Document date
            tags: List of tags
            created_by: User ID who created the document
            
        Returns:
            Tuple of (success, message, document)
        """
        try:
            # Validate file
            is_valid, error_msg = self.file_service.validate_file(file_path)
            if not is_valid:
                return False, error_msg, None
            
            # Get file information
            file_info = self.file_service.get_file_info(file_path)
            if not file_info:
                return False, "فشل في الحصول على معلومات الملف", None
            
            with self.db_manager.get_session() as session:
                # Create document record first to get ID
                document = Document(
                    title=title,
                    description=description,
                    file_name=file_info['name'],
                    file_path="",  # Will be updated after file storage
                    file_size=file_info['size'],
                    file_type=file_info['extension'],
                    mime_type=file_info['mime_type'],
                    category_id=category_id,
                    created_by=created_by,
                    document_date=document_date or datetime.now().date(),
                    tags=', '.join(tags) if tags else None
                )
                
                session.add(document)
                session.flush()  # Get the ID without committing
                
                # Store file
                success, error_msg, stored_path = self.file_service.store_file(
                    file_path, document.id
                )
                
                if not success:
                    session.rollback()
                    return False, error_msg, None
                
                # Update document with file path
                document.file_path = stored_path
                
                # Extract text content for search indexing
                text_content = self.file_service.extract_text_content(file_path)
                if text_content:
                    document.full_text_content = text_content[:10000]  # Limit size
                
                # Create thumbnail
                thumbnail_path = self.file_service.create_thumbnail(file_path, document.id)
                if thumbnail_path:
                    document.thumbnail_path = thumbnail_path
                
                session.commit()
                session.refresh(document)
                
                # Log audit trail
                if created_by:
                    AuditLog.log_document_action(
                        session, created_by, "create", document.id, document.title
                    )
                
                self.logger.info(f"تم إنشاء وثيقة جديدة: {document.title} (ID: {document.id})")
                return True, "تم إنشاء الوثيقة بنجاح", document
                
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء الوثيقة: {e}")
            return False, f"خطأ في إنشاء الوثيقة: {e}", None
    
    def update_document(self, document_id: int, updates: Dict[str, Any],
                       updated_by: int) -> Tuple[bool, str, Optional[Document]]:
        """
        Update document metadata
        
        Args:
            document_id: Document ID
            updates: Dictionary of updates
            updated_by: User ID who updated the document
            
        Returns:
            Tuple of (success, message, document)
        """
        try:
            with self.db_manager.get_session() as session:
                document = session.query(Document).get(document_id)
                if not document:
                    return False, "الوثيقة غير موجودة", None
                
                # Store old values for audit
                old_values = document.to_dict()
                
                # Update fields
                allowed_fields = [
                    'title', 'description', 'category_id', 'document_date', 'tags'
                ]
                
                for field, value in updates.items():
                    if field in allowed_fields and hasattr(document, field):
                        setattr(document, field, value)
                
                document.updated_at = datetime.utcnow()
                session.commit()
                session.refresh(document)
                
                # Log audit trail
                new_values = document.to_dict()
                AuditLog.log_action(
                    session, updated_by, "document_update", "documents", document.id,
                    old_values, new_values
                )
                
                self.logger.info(f"تم تحديث الوثيقة: {document.title} (ID: {document.id})")
                return True, "تم تحديث الوثيقة بنجاح", document
                
        except Exception as e:
            self.logger.error(f"خطأ في تحديث الوثيقة: {e}")
            return False, f"خطأ في تحديث الوثيقة: {e}", None
    
    def delete_document(self, document_id: int, deleted_by: int,
                       permanent: bool = False) -> Tuple[bool, str]:
        """
        Delete document
        
        Args:
            document_id: Document ID
            deleted_by: User ID who deleted the document
            permanent: Whether to permanently delete or soft delete
            
        Returns:
            Tuple of (success, message)
        """
        try:
            with self.db_manager.get_session() as session:
                document = session.query(Document).get(document_id)
                if not document:
                    return False, "الوثيقة غير موجودة"
                
                # Store document info for audit
                document_info = document.to_dict()
                
                if permanent:
                    # Delete physical files
                    if document.file_path:
                        self.file_service.delete_file(document.file_path)
                    
                    if document.thumbnail_path:
                        self.file_service.delete_file(document.thumbnail_path)
                    
                    # Delete from database
                    session.delete(document)
                    action = "delete_permanent"
                else:
                    # Soft delete
                    document.is_active = False
                    document.updated_at = datetime.utcnow()
                    action = "delete_soft"
                
                session.commit()
                
                # Log audit trail
                AuditLog.log_action(
                    session, deleted_by, action, "documents", document_id,
                    document_info, None
                )
                
                delete_type = "نهائياً" if permanent else "مؤقتاً"
                self.logger.info(f"تم حذف الوثيقة {delete_type}: {document_info['title']} (ID: {document_id})")
                return True, f"تم حذف الوثيقة {delete_type}"
                
        except Exception as e:
            self.logger.error(f"خطأ في حذف الوثيقة: {e}")
            return False, f"خطأ في حذف الوثيقة: {e}"
    
    def get_document(self, document_id: int) -> Optional[Document]:
        """
        Get document by ID
        
        Args:
            document_id: Document ID
            
        Returns:
            Document instance or None
        """
        try:
            with self.db_manager.get_session() as session:
                return session.query(Document).filter(
                    Document.id == document_id,
                    Document.is_active == True
                ).first()
                
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على الوثيقة: {e}")
            return None
    
    def get_documents_by_category(self, category_id: int, include_subcategories: bool = False,
                                 limit: int = 100, offset: int = 0) -> List[Document]:
        """
        Get documents by category
        
        Args:
            category_id: Category ID
            include_subcategories: Include documents from subcategories
            limit: Maximum number of documents
            offset: Offset for pagination
            
        Returns:
            List of documents
        """
        try:
            with self.db_manager.get_session() as session:
                return Document.get_by_category(
                    session, category_id, include_subcategories
                )[offset:offset + limit]
                
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على وثائق الفئة: {e}")
            return []
    
    def get_recent_documents(self, user_id: Optional[int] = None,
                           limit: int = 10) -> List[Document]:
        """
        Get recent documents
        
        Args:
            user_id: Filter by user (optional)
            limit: Maximum number of documents
            
        Returns:
            List of recent documents
        """
        try:
            with self.db_manager.get_session() as session:
                return Document.get_recent(session, limit, user_id)
                
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على الوثائق الحديثة: {e}")
            return []
    
    def import_folder(self, folder_path: Path, category_id: Optional[int] = None,
                     created_by: int = None, progress_callback=None) -> Dict[str, Any]:
        """
        Import all files from a folder
        
        Args:
            folder_path: Path to folder
            category_id: Category ID for imported documents
            created_by: User ID who imported the documents
            progress_callback: Callback function for progress updates
            
        Returns:
            Dictionary with import results
        """
        try:
            if not folder_path.exists() or not folder_path.is_dir():
                return {
                    'success': False,
                    'message': 'المجلد غير موجود',
                    'imported': 0,
                    'failed': 0,
                    'errors': []
                }
            
            # Get all files in folder
            files = [f for f in folder_path.rglob('*') if f.is_file()]
            total_files = len(files)
            
            imported = 0
            failed = 0
            errors = []
            
            for i, file_path in enumerate(files):
                try:
                    # Update progress
                    if progress_callback:
                        progress_callback(i + 1, total_files, file_path.name)
                    
                    # Create document
                    success, message, document = self.create_document(
                        file_path=file_path,
                        title=file_path.stem,  # Use filename without extension as title
                        description=f"مستورد من: {folder_path}",
                        category_id=category_id,
                        created_by=created_by
                    )
                    
                    if success:
                        imported += 1
                    else:
                        failed += 1
                        errors.append(f"{file_path.name}: {message}")
                        
                except Exception as e:
                    failed += 1
                    errors.append(f"{file_path.name}: {str(e)}")
            
            return {
                'success': True,
                'message': f'تم استيراد {imported} من {total_files} ملف',
                'imported': imported,
                'failed': failed,
                'errors': errors
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في استيراد المجلد: {e}")
            return {
                'success': False,
                'message': f'خطأ في استيراد المجلد: {e}',
                'imported': 0,
                'failed': 0,
                'errors': [str(e)]
            }
    
    def get_document_statistics(self) -> Dict[str, Any]:
        """
        Get document statistics
        
        Returns:
            Dictionary with statistics
        """
        try:
            with self.db_manager.get_session() as session:
                total_documents = session.query(Document).filter(Document.is_active == True).count()
                
                # Documents by file type
                file_type_stats = {}
                file_types = session.query(Document.file_type).filter(Document.is_active == True).distinct().all()
                
                for file_type in file_types:
                    count = session.query(Document).filter(
                        Document.file_type == file_type[0],
                        Document.is_active == True
                    ).count()
                    file_type_stats[file_type[0]] = count
                
                # Storage usage
                storage_usage = self.file_service.get_storage_usage()
                
                return {
                    'total_documents': total_documents,
                    'file_type_stats': file_type_stats,
                    'storage_usage': storage_usage
                }
                
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على إحصائيات الوثائق: {e}")
            return {}
