# 🚀 دليل التشغيل السريع - Quick Start Guide

## نظام الأرشفة الإلكترونية المتكامل
## Complete Electronic Archive System

---

## 📋 الميزات الأساسية - Core Features

### 🗃️ **إدارة الوثائق الشاملة**
- ✅ إضافة/تعديل/عرض/حذف الوثائق
- ✅ بيانات وصفية متكاملة (عنوان، وصف، فئة، علامات)
- ✅ دعم المرفقات المتعددة (PDF، صور، وورد، إكسل)
- ✅ نظام حذف مرن (استعادة أو حذف نهائي)

### 🔍 **البحث والاسترجاع المتقدم**
- ✅ بحث نصي كامل باستخدام SQLite FTS5
- ✅ لوحة بحث ذكية مع فلاتر متقدمة
- ✅ تمييز النتائج في الوثائق
- ✅ بحث سريع وبحث متقدم

### 🖼️ **العروض المرئية**
- ✅ توليد صور مصغرة للوثائق تلقائياً
- ✅ معاينة مدمجة للصور وملفات PDF
- ✅ دعم السمات الفاتحة والداكنة
- ✅ واجهة عصرية ومتجاوبة

### 📱 **المدخلات والمخرجات**
- ✅ دعم الماسح الضوئي عبر WIA (Windows)
- ✅ استيراد الملفات كبديل لأنظمة أخرى
- ✅ تصدير وطباعة الوثائق والمرفقات
- ✅ تصدير مجمع لعدة وثائق

---

## 🚀 التشغيل السريع - Quick Launch

### الطريقة الأولى: التشغيل التلقائي (Windows)
```bash
# انقر مرتين على الملف
start_archive.bat
```

### الطريقة الثانية: المشغل التفاعلي
```bash
python quick_archive.py
```

### الطريقة الثالثة: تشغيل مباشر
```bash
# النظام المتكامل (مستحسن)
python archive_system.py

# النظام المتقدم
python advanced_app.py

# النظام المبسط
python main_simple.py

# النظام التجريبي
python demo_app.py
```

---

## 📦 المتطلبات - Requirements

### المتطلبات الأساسية:
```bash
pip install PySide6 sqlalchemy bcrypt python-dateutil
```

### المتطلبات الاختيارية (للميزات المتقدمة):
```bash
pip install Pillow PyPDF2 python-docx pdf2image
```

### للمسح الضوئي (Windows فقط):
```bash
pip install pywin32 comtypes
```

---

## 🎯 التطبيقات المتاحة - Available Applications

| التطبيق | الوصف | الاستخدام المناسب |
|---------|--------|------------------|
| `archive_system.py` | **النظام المتكامل** | الاستخدام الأساسي المستحسن |
| `advanced_app.py` | **النظام المتقدم** | للميزات المتقدمة والمطورين |
| `main_simple.py` | **النظام المبسط** | للاستخدام البسيط مع SQLite |
| `demo_app.py` | **النظام التجريبي** | للتعرف على الواجهة |
| `test_app.py` | **تطبيق الاختبار** | لحل المشاكل التقنية |

---

## 🔐 بيانات تسجيل الدخول الافتراضية

```
اسم المستخدم: admin
كلمة المرور: Admin123!
```

**⚠️ مهم:** يرجى تغيير كلمة المرور بعد أول تسجيل دخول!

---

## 📁 هيكل المشروع - Project Structure

```
Archive_System/
├── 🚀 archive_system.py          # النظام المتكامل
├── 🔧 quick_archive.py           # المشغل التفاعلي
├── 🖥️ start_archive.bat         # تشغيل تلقائي (Windows)
├── 📖 QUICK_START.md             # هذا الدليل
│
├── 📂 src/                       # الكود المصدري
│   ├── 🏗️ core/                 # الوظائف الأساسية
│   ├── 🗃️ models/               # نماذج البيانات
│   ├── 🖼️ ui/                   # واجهة المستخدم
│   ├── ⚙️ services/             # الخدمات
│   └── 🛠️ utils/                # الأدوات المساعدة
│
├── 🧪 tests/                     # الاختبارات
├── 📁 documents/                 # تخزين الوثائق
├── 🖼️ thumbnails/               # الصور المصغرة
├── 📂 temp/                      # الملفات المؤقتة
├── 💾 backup/                    # النسخ الاحتياطية
└── 📋 logs/                      # ملفات السجلات
```

---

## 🎮 كيفية الاستخدام - How to Use

### 1️⃣ **البدء**
1. شغل التطبيق باستخدام إحدى الطرق أعلاه
2. سجل الدخول باستخدام البيانات الافتراضية
3. ستظهر النافذة الرئيسية مع جميع الميزات

### 2️⃣ **إضافة وثيقة جديدة**
1. انقر على "إضافة وثيقة" أو اضغط `Ctrl+N`
2. اختر الملف من جهازك
3. أدخل العنوان والوصف
4. اختر الفئة وأضف العلامات
5. انقر "حفظ"

### 3️⃣ **البحث في الوثائق**
1. استخدم مربع البحث في الأعلى
2. اكتب كلمات البحث واضغط Enter
3. استخدم "بحث متقدم" للمرشحات الإضافية
4. ستظهر النتائج مع تمييز كلمات البحث

### 4️⃣ **المسح الضوئي** (Windows)
1. انقر على "مسح ضوئي" أو اضغط `Ctrl+S`
2. اختر الماسح الضوئي
3. اضبط الإعدادات (DPI، ألوان)
4. امسح الوثيقة وأضف البيانات الوصفية

### 5️⃣ **تصدير الوثائق**
1. اختر الوثائق المطلوبة
2. انقر على "تصدير"
3. اختر تنسيق التصدير (ZIP، مجلد)
4. حدد مكان الحفظ

---

## 🎨 تخصيص المظهر - Theme Customization

### تبديل المظهر:
- **اختصار لوحة المفاتيح:** `Ctrl+T`
- **من القائمة:** عرض → تبديل المظهر
- **تلقائي:** يتبع إعدادات النظام

### المظاهر المتاحة:
- 🌞 **المظهر الفاتح** - للاستخدام النهاري
- 🌙 **المظهر الداكن** - للاستخدام الليلي أو تقليل إجهاد العين

---

## 🔧 حل المشاكل الشائعة - Troubleshooting

### مشكلة: "ModuleNotFoundError"
```bash
# الحل: تثبيت المكتبة المفقودة
pip install [اسم المكتبة]

# أو تثبيت جميع المتطلبات
pip install PySide6 sqlalchemy bcrypt python-dateutil
```

### مشكلة: "لا يعمل المسح الضوئي"
- تأكد من أنك على نظام Windows
- تأكد من تثبيت `pywin32` و `comtypes`
- تأكد من توصيل الماسح الضوئي وتشغيله

### مشكلة: "خطأ في قاعدة البيانات"
- سيتم إنشاء قاعدة بيانات SQLite تلقائياً
- تأكد من وجود صلاحيات الكتابة في مجلد التطبيق

### مشكلة: "لا تظهر الصور المصغرة"
- تأكد من تثبيت `Pillow`
- تأكد من وجود مجلد `thumbnails`

---

## 📞 الدعم والمساعدة - Support

### للحصول على المساعدة:
1. **راجع ملفات السجلات:** `logs/archive.log`
2. **شغل تطبيق الاختبار:** `python test_app.py`
3. **تحقق من المتطلبات:** `python quick_archive.py` → خيار `i`

### الملفات المهمة:
- `config.ini` - إعدادات التطبيق
- `archive.db` - قاعدة البيانات
- `logs/archive.log` - سجل الأحداث

---

## 🎉 نصائح للاستخدام الأمثل

### 📝 **تنظيم الوثائق:**
- استخدم فئات واضحة ومنطقية
- أضف علامات وصفية للبحث السهل
- استخدم أسماء ملفات واضحة

### 🔍 **البحث الفعال:**
- استخدم كلمات مفتاحية محددة
- جرب البحث المتقدم للنتائج الدقيقة
- استفد من المرشحات (التاريخ، النوع، الحجم)

### 💾 **النسخ الاحتياطية:**
- انسخ مجلد `documents` بانتظام
- احتفظ بنسخة من `archive.db`
- استخدم ميزة التصدير للأرشفة الخارجية

### 🔒 **الأمان:**
- غيّر كلمة مرور المدير الافتراضية
- أنشئ مستخدمين منفصلين للفرق المختلفة
- راجع سجل التدقيق بانتظام

---

## 🚀 البدء الآن!

```bash
# الطريقة الأسرع للبدء:
python quick_archive.py
```

**🎊 مبروك! نظام الأرشفة الإلكترونية جاهز للاستخدام!**

---

*آخر تحديث: 2025-08-18*
