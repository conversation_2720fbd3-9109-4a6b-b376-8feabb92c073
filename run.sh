#!/bin/bash
# Shell script to run Electronic Archive System on Linux/macOS
# ملف تشغيل نظام الأرشفة الإلكترونية على Linux/macOS

echo "========================================"
echo "Electronic Archive System"
echo "نظام الأرشفة الإلكترونية"
echo "========================================"

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "Python 3 is not installed"
    echo "Python 3 غير مثبت"
    exit 1
fi

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    echo "إنشاء البيئة الافتراضية..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "Activating virtual environment..."
echo "تفعيل البيئة الافتراضية..."
source venv/bin/activate

# Install requirements
echo "Installing requirements..."
echo "تثبيت المتطلبات..."
pip install -r requirements.txt

# Check if database is set up
if [ ! -f "config.ini" ]; then
    echo "Configuration file not found. Running database setup..."
    echo "ملف التكوين غير موجود. تشغيل إعداد قاعدة البيانات..."
    python setup_database.py
fi

# Run the application
echo "Starting Electronic Archive System..."
echo "بدء تشغيل نظام الأرشفة الإلكترونية..."
python main.py
