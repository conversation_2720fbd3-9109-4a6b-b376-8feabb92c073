# Electronic Archive System | نظام الأرشفة الإلكترونية

A production-ready Electronic Archiving System built with Python 3.11+ and PySide6 (Qt 6) with SQL Server, designed to run primarily on Windows 10+, with graceful degradation on other OSes.

## Features | الميزات

### Core Features | الميزات الأساسية
- 📁 **Document Management** | إدارة الوثائق
- 🔍 **Full-text Search** | البحث النصي الكامل
- 🗄️ **SQL Server Integration** | تكامل مع SQL Server
- 🖼️ **PDF/Image Preview** | معاينة PDF والصور
- 📱 **Integrated Scanning** | المسح الضوئي المدمج
- 🔒 **Secure File Storage** | تخزين آمن للملفات
- 📊 **Metadata Management** | إدارة البيانات الوصفية
- 📎 **Attachments Support** | دعم المرفقات
- 📋 **Audit Trails** | سجلات التدقيق

### Technical Features | الميزات التقنية
- 🖥️ **Windows 10+ Optimized** | محسن لـ Windows 10+
- 🌐 **Multi-language Support** | دعم متعدد اللغات
- 🎨 **Modern Qt6 Interface** | واجهة Qt6 حديثة
- 🔐 **Enterprise Security** | أمان على مستوى المؤسسات
- 📈 **Scalable Architecture** | بنية قابلة للتوسع

## Requirements | المتطلبات

### System Requirements | متطلبات النظام
- Windows 10+ (Primary) | Windows 10+ (أساسي)
- Python 3.11+
- SQL Server 2019+ or SQL Server Express
- 4GB RAM minimum, 8GB recommended
- 10GB free disk space

### Python Dependencies | تبعيات Python
```bash
pip install -r requirements.txt
```

## Installation | التثبيت

1. **Clone the repository | استنساخ المستودع**
```bash
git clone https://github.com/your-org/electronic-archive-system.git
cd electronic-archive-system
```

2. **Install dependencies | تثبيت التبعيات**
```bash
pip install -r requirements.txt
```

3. **Configure database | تكوين قاعدة البيانات**
   - Edit `config.ini` with your SQL Server settings
   - Run database initialization

4. **Run the application | تشغيل التطبيق**
```bash
python main.py
```

## Project Structure | بنية المشروع

```
Archive_new/
├── main.py                 # Application entry point | نقطة دخول التطبيق
├── config.ini             # Configuration file | ملف التكوين
├── requirements.txt       # Python dependencies | تبعيات Python
├── src/                   # Source code | الكود المصدري
│   ├── core/             # Core functionality | الوظائف الأساسية
│   ├── ui/               # User interface | واجهة المستخدم
│   ├── models/           # Data models | نماذج البيانات
│   ├── services/         # Business logic | منطق العمل
│   └── utils/            # Utilities | الأدوات المساعدة
├── resources/            # Resources | الموارد
│   ├── icons/           # Application icons | أيقونات التطبيق
│   └── translations/    # Language files | ملفات اللغة
├── documents/           # Document storage | تخزين الوثائق
├── thumbnails/          # Thumbnail cache | ذاكرة التخزين المؤقت للصور المصغرة
├── temp/               # Temporary files | الملفات المؤقتة
├── backup/             # Backup storage | تخزين النسخ الاحتياطية
├── logs/               # Application logs | سجلات التطبيق
└── tests/              # Unit tests | اختبارات الوحدة
```

## Configuration | التكوين

Edit `config.ini` to customize the application settings:

- **Database**: SQL Server connection settings
- **Storage**: File storage paths and limits
- **Security**: Encryption and authentication settings
- **Scanning**: Scanner configuration
- **Search**: Search engine settings

## Usage | الاستخدام

1. **Start the application** | بدء التطبيق
2. **Configure database connection** | تكوين اتصال قاعدة البيانات
3. **Add document categories** | إضافة فئات الوثائق
4. **Import or scan documents** | استيراد أو مسح الوثائق
5. **Search and manage documents** | البحث وإدارة الوثائق

## Development | التطوير

### Running Tests | تشغيل الاختبارات
```bash
pytest tests/
```

### Code Formatting | تنسيق الكود
```bash
black src/
flake8 src/
```

## License | الترخيص

This project is licensed under the MIT License - see the LICENSE file for details.

## Support | الدعم

For support and questions, please contact: <EMAIL>
