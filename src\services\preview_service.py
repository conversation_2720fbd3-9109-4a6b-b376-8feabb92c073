"""
Preview Service for Electronic Archive System
خدمة المعاينة لنظام الأرشفة الإلكترونية

Handles thumbnails and previews for images, PDFs, and documents
"""

import logging
import os
from pathlib import Path
from typing import Optional, Tuple, List
from PIL import Image, ImageOps
import tempfile

from ..core.config import Config


class PreviewService:
    """
    Service for generating thumbnails and previews
    خدمة إنشاء الصور المصغرة والمعاينات
    """
    
    def __init__(self, config: Config):
        """
        Initialize preview service
        
        Args:
            config: Configuration manager
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Thumbnail settings
        self.thumbnail_size = (200, 200)
        self.preview_size = (800, 600)
        
        # Supported formats
        self.image_formats = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp'}
        self.pdf_formats = {'.pdf'}
        self.office_formats = {'.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'}
        
        # Ensure thumbnail directory exists
        self.thumbnail_dir = Path(config.storage_paths['thumbnails'])
        self.thumbnail_dir.mkdir(parents=True, exist_ok=True)
    
    def generate_thumbnail(self, file_path: Path, document_id: int, force_regenerate: bool = False) -> Optional[str]:
        """
        Generate thumbnail for a file
        
        Args:
            file_path: Path to source file
            document_id: Document ID
            force_regenerate: Force regeneration even if thumbnail exists
            
        Returns:
            Path to thumbnail or None if failed
        """
        try:
            # Generate thumbnail filename
            thumbnail_path = self.thumbnail_dir / f"thumb_{document_id}.jpg"
            
            # Check if thumbnail already exists
            if thumbnail_path.exists() and not force_regenerate:
                return str(thumbnail_path)
            
            # Check file extension
            file_extension = file_path.suffix.lower()
            
            if file_extension in self.image_formats:
                return self._generate_image_thumbnail(file_path, thumbnail_path)
            elif file_extension in self.pdf_formats:
                return self._generate_pdf_thumbnail(file_path, thumbnail_path)
            elif file_extension in self.office_formats:
                return self._generate_office_thumbnail(file_path, thumbnail_path)
            else:
                return self._generate_generic_thumbnail(file_path, thumbnail_path)
                
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء الصورة المصغرة: {e}")
            return None
    
    def _generate_image_thumbnail(self, source_path: Path, thumbnail_path: Path) -> Optional[str]:
        """Generate thumbnail for image files"""
        try:
            with Image.open(source_path) as img:
                # Convert to RGB if necessary
                if img.mode in ('RGBA', 'LA', 'P'):
                    # Create white background for transparent images
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    if img.mode == 'P':
                        img = img.convert('RGBA')
                    background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                    img = background
                elif img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # Auto-orient based on EXIF data
                img = ImageOps.exif_transpose(img)
                
                # Create thumbnail
                img.thumbnail(self.thumbnail_size, Image.Resampling.LANCZOS)
                
                # Save thumbnail
                img.save(thumbnail_path, 'JPEG', quality=85, optimize=True)
                
                return str(thumbnail_path)
                
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء صورة مصغرة للصورة: {e}")
            return None
    
    def _generate_pdf_thumbnail(self, source_path: Path, thumbnail_path: Path) -> Optional[str]:
        """Generate thumbnail for PDF files"""
        try:
            # Try using pdf2image if available
            try:
                from pdf2image import convert_from_path
                
                # Convert first page to image
                pages = convert_from_path(source_path, first_page=1, last_page=1, dpi=150)
                
                if pages:
                    page_image = pages[0]
                    
                    # Convert to RGB if necessary
                    if page_image.mode != 'RGB':
                        page_image = page_image.convert('RGB')
                    
                    # Create thumbnail
                    page_image.thumbnail(self.thumbnail_size, Image.Resampling.LANCZOS)
                    
                    # Save thumbnail
                    page_image.save(thumbnail_path, 'JPEG', quality=85, optimize=True)
                    
                    return str(thumbnail_path)
                    
            except ImportError:
                self.logger.warning("pdf2image غير متوفر، استخدام الصورة العامة للـ PDF")
                return self._generate_generic_thumbnail(source_path, thumbnail_path, "PDF")
                
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء صورة مصغرة للـ PDF: {e}")
            return self._generate_generic_thumbnail(source_path, thumbnail_path, "PDF")
    
    def _generate_office_thumbnail(self, source_path: Path, thumbnail_path: Path) -> Optional[str]:
        """Generate thumbnail for Office documents"""
        try:
            # For now, generate generic thumbnail
            # In the future, could use LibreOffice or other tools for conversion
            file_type = source_path.suffix.upper().lstrip('.')
            return self._generate_generic_thumbnail(source_path, thumbnail_path, file_type)
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء صورة مصغرة للوثيقة: {e}")
            return None
    
    def _generate_generic_thumbnail(self, source_path: Path, thumbnail_path: Path, 
                                  file_type: Optional[str] = None) -> Optional[str]:
        """Generate generic thumbnail with file type icon"""
        try:
            # Create a simple thumbnail with file type text
            img = Image.new('RGB', self.thumbnail_size, color=(240, 240, 240))
            
            # You could add text or icon here
            # For now, just save the plain thumbnail
            img.save(thumbnail_path, 'JPEG', quality=85)
            
            return str(thumbnail_path)
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء الصورة المصغرة العامة: {e}")
            return None
    
    def generate_preview(self, file_path: Path, document_id: int) -> Optional[str]:
        """
        Generate preview for a file
        
        Args:
            file_path: Path to source file
            document_id: Document ID
            
        Returns:
            Path to preview or None if failed
        """
        try:
            # Generate preview filename
            preview_path = self.thumbnail_dir / f"preview_{document_id}.jpg"
            
            # Check if preview already exists
            if preview_path.exists():
                return str(preview_path)
            
            file_extension = file_path.suffix.lower()
            
            if file_extension in self.image_formats:
                return self._generate_image_preview(file_path, preview_path)
            elif file_extension in self.pdf_formats:
                return self._generate_pdf_preview(file_path, preview_path)
            else:
                # For other files, use thumbnail as preview
                thumbnail = self.generate_thumbnail(file_path, document_id)
                return thumbnail
                
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء المعاينة: {e}")
            return None
    
    def _generate_image_preview(self, source_path: Path, preview_path: Path) -> Optional[str]:
        """Generate preview for image files"""
        try:
            with Image.open(source_path) as img:
                # Convert to RGB if necessary
                if img.mode in ('RGBA', 'LA', 'P'):
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    if img.mode == 'P':
                        img = img.convert('RGBA')
                    background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                    img = background
                elif img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # Auto-orient based on EXIF data
                img = ImageOps.exif_transpose(img)
                
                # Resize for preview (maintain aspect ratio)
                img.thumbnail(self.preview_size, Image.Resampling.LANCZOS)
                
                # Save preview
                img.save(preview_path, 'JPEG', quality=90, optimize=True)
                
                return str(preview_path)
                
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء معاينة الصورة: {e}")
            return None
    
    def _generate_pdf_preview(self, source_path: Path, preview_path: Path) -> Optional[str]:
        """Generate preview for PDF files"""
        try:
            # Try using pdf2image if available
            try:
                from pdf2image import convert_from_path
                
                # Convert first page to image with higher DPI for preview
                pages = convert_from_path(source_path, first_page=1, last_page=1, dpi=200)
                
                if pages:
                    page_image = pages[0]
                    
                    # Convert to RGB if necessary
                    if page_image.mode != 'RGB':
                        page_image = page_image.convert('RGB')
                    
                    # Resize for preview
                    page_image.thumbnail(self.preview_size, Image.Resampling.LANCZOS)
                    
                    # Save preview
                    page_image.save(preview_path, 'JPEG', quality=90, optimize=True)
                    
                    return str(preview_path)
                    
            except ImportError:
                self.logger.warning("pdf2image غير متوفر للمعاينة")
                return None
                
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء معاينة PDF: {e}")
            return None
    
    def get_supported_formats(self) -> List[str]:
        """Get list of supported file formats"""
        return list(self.image_formats | self.pdf_formats | self.office_formats)
    
    def is_previewable(self, file_path: Path) -> bool:
        """Check if file can be previewed"""
        file_extension = file_path.suffix.lower()
        return file_extension in (self.image_formats | self.pdf_formats)
    
    def cleanup_thumbnails(self, document_ids: List[int]):
        """
        Clean up thumbnails for deleted documents
        
        Args:
            document_ids: List of document IDs to clean up
        """
        try:
            for doc_id in document_ids:
                thumbnail_path = self.thumbnail_dir / f"thumb_{doc_id}.jpg"
                preview_path = self.thumbnail_dir / f"preview_{doc_id}.jpg"
                
                if thumbnail_path.exists():
                    thumbnail_path.unlink()
                
                if preview_path.exists():
                    preview_path.unlink()
                    
            self.logger.info(f"تم تنظيف {len(document_ids)} صورة مصغرة")
            
        except Exception as e:
            self.logger.error(f"خطأ في تنظيف الصور المصغرة: {e}")
    
    def get_thumbnail_stats(self) -> dict:
        """Get thumbnail storage statistics"""
        try:
            thumbnail_files = list(self.thumbnail_dir.glob("thumb_*.jpg"))
            preview_files = list(self.thumbnail_dir.glob("preview_*.jpg"))
            
            total_size = sum(f.stat().st_size for f in thumbnail_files + preview_files)
            
            return {
                'thumbnail_count': len(thumbnail_files),
                'preview_count': len(preview_files),
                'total_size_bytes': total_size,
                'total_size_mb': round(total_size / (1024 * 1024), 2)
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في حساب إحصائيات الصور المصغرة: {e}")
            return {
                'thumbnail_count': 0,
                'preview_count': 0,
                'total_size_bytes': 0,
                'total_size_mb': 0
            }
