"""
Export and Print Service for Electronic Archive System
خدمة التصدير والطباعة لنظام الأرشفة الإلكترونية

Handles document export and printing functionality
"""

import logging
import shutil
import zipfile
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime

from PySide6.QtPrintSupport import QPrinter, QPrintDialog
from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtGui import QPainter, QPixmap, QTextDocument
from PySide6.QtCore import QUrl, QMarginsF

from ..core.config import Config


class ExportService:
    """
    Service for exporting and printing documents
    خدمة تصدير وطباعة الوثائق
    """
    
    def __init__(self, config: Config):
        """
        Initialize export service
        
        Args:
            config: Configuration manager
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Export settings
        self.temp_dir = Path(config.storage_paths['temp'])
        self.temp_dir.mkdir(parents=True, exist_ok=True)
    
    def export_documents(self, documents: List[Dict[str, Any]], 
                        export_path: str, export_format: str = "zip") -> bool:
        """
        Export multiple documents
        
        Args:
            documents: List of document dictionaries
            export_path: Path to export to
            export_format: Export format ('zip', 'folder')
            
        Returns:
            True if successful
        """
        try:
            export_path = Path(export_path)
            
            if export_format.lower() == "zip":
                return self._export_to_zip(documents, export_path)
            else:
                return self._export_to_folder(documents, export_path)
                
        except Exception as e:
            self.logger.error(f"خطأ في تصدير الوثائق: {e}")
            return False
    
    def _export_to_zip(self, documents: List[Dict[str, Any]], zip_path: Path) -> bool:
        """Export documents to ZIP file"""
        try:
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # Add documents
                for doc in documents:
                    file_path = Path(doc['file_path'])
                    if file_path.exists():
                        # Create safe filename
                        safe_name = self._sanitize_filename(doc['title'])
                        extension = file_path.suffix
                        zip_filename = f"{safe_name}{extension}"
                        
                        # Ensure unique filename in zip
                        counter = 1
                        original_zip_filename = zip_filename
                        while zip_filename in [info.filename for info in zipf.infolist()]:
                            name_part = Path(original_zip_filename).stem
                            extension = Path(original_zip_filename).suffix
                            zip_filename = f"{name_part}_{counter}{extension}"
                            counter += 1
                        
                        zipf.write(file_path, zip_filename)
                        
                        # Add metadata file
                        metadata = self._create_metadata_text(doc)
                        metadata_filename = f"{Path(zip_filename).stem}_metadata.txt"
                        zipf.writestr(metadata_filename, metadata)
                
                # Add export summary
                summary = self._create_export_summary(documents)
                zipf.writestr("export_summary.txt", summary)
            
            self.logger.info(f"تم تصدير {len(documents)} وثيقة إلى ZIP: {zip_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء ملف ZIP: {e}")
            return False
    
    def _export_to_folder(self, documents: List[Dict[str, Any]], folder_path: Path) -> bool:
        """Export documents to folder"""
        try:
            # Create export folder
            folder_path.mkdir(parents=True, exist_ok=True)
            
            exported_count = 0
            for doc in documents:
                file_path = Path(doc['file_path'])
                if file_path.exists():
                    # Create safe filename
                    safe_name = self._sanitize_filename(doc['title'])
                    extension = file_path.suffix
                    export_filename = f"{safe_name}{extension}"
                    
                    # Ensure unique filename
                    export_file_path = folder_path / export_filename
                    counter = 1
                    while export_file_path.exists():
                        name_part = Path(export_filename).stem
                        extension = Path(export_filename).suffix
                        export_filename = f"{name_part}_{counter}{extension}"
                        export_file_path = folder_path / export_filename
                        counter += 1
                    
                    # Copy file
                    shutil.copy2(file_path, export_file_path)
                    
                    # Create metadata file
                    metadata = self._create_metadata_text(doc)
                    metadata_file = export_file_path.with_suffix('.txt')
                    with open(metadata_file, 'w', encoding='utf-8') as f:
                        f.write(metadata)
                    
                    exported_count += 1
            
            # Create export summary
            summary = self._create_export_summary(documents)
            summary_file = folder_path / "export_summary.txt"
            with open(summary_file, 'w', encoding='utf-8') as f:
                f.write(summary)
            
            self.logger.info(f"تم تصدير {exported_count} وثيقة إلى المجلد: {folder_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في تصدير إلى المجلد: {e}")
            return False
    
    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for safe export"""
        import re
        
        # Remove or replace invalid characters
        invalid_chars = r'[<>:"/\\|?*]'
        sanitized = re.sub(invalid_chars, '_', filename)
        
        # Remove leading/trailing spaces and dots
        sanitized = sanitized.strip(' .')
        
        # Limit length
        if len(sanitized) > 200:
            sanitized = sanitized[:200]
        
        return sanitized or "document"
    
    def _create_metadata_text(self, doc: Dict[str, Any]) -> str:
        """Create metadata text for document"""
        metadata_lines = [
            "معلومات الوثيقة - Document Metadata",
            "=" * 40,
            f"العنوان - Title: {doc.get('title', 'N/A')}",
            f"الوصف - Description: {doc.get('description', 'N/A')}",
            f"نوع الملف - File Type: {doc.get('file_type', 'N/A')}",
            f"حجم الملف - File Size: {self._format_file_size(doc.get('file_size', 0))}",
            f"تاريخ الإنشاء - Created: {doc.get('created_at', 'N/A')}",
            f"تاريخ الوثيقة - Document Date: {doc.get('document_date', 'N/A')}",
            f"العلامات - Tags: {doc.get('tags', 'N/A')}",
            f"المنشئ - Created By: {doc.get('created_by', 'N/A')}",
            "",
            f"تم التصدير في - Exported on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        ]
        
        return "\n".join(metadata_lines)
    
    def _create_export_summary(self, documents: List[Dict[str, Any]]) -> str:
        """Create export summary"""
        total_size = sum(doc.get('file_size', 0) for doc in documents)
        
        summary_lines = [
            "ملخص التصدير - Export Summary",
            "=" * 40,
            f"عدد الوثائق - Total Documents: {len(documents)}",
            f"الحجم الإجمالي - Total Size: {self._format_file_size(total_size)}",
            f"تاريخ التصدير - Export Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "",
            "قائمة الوثائق - Document List:",
            "-" * 40
        ]
        
        for i, doc in enumerate(documents, 1):
            summary_lines.append(f"{i}. {doc.get('title', 'Untitled')} ({doc.get('file_type', 'Unknown')})")
        
        return "\n".join(summary_lines)
    
    def _format_file_size(self, size_bytes: int) -> str:
        """Format file size in human readable format"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        size = float(size_bytes)
        
        while size >= 1024 and i < len(size_names) - 1:
            size /= 1024.0
            i += 1
        
        return f"{size:.1f} {size_names[i]}"
    
    def print_document(self, document_path: str, print_settings: Optional[Dict[str, Any]] = None) -> bool:
        """
        Print a document
        
        Args:
            document_path: Path to document to print
            print_settings: Print settings
            
        Returns:
            True if successful
        """
        try:
            file_path = Path(document_path)
            if not file_path.exists():
                self.logger.error(f"الملف غير موجود: {document_path}")
                return False
            
            file_extension = file_path.suffix.lower()
            
            if file_extension in ['.jpg', '.jpeg', '.png', '.bmp', '.gif']:
                return self._print_image(file_path, print_settings)
            elif file_extension == '.pdf':
                return self._print_pdf(file_path, print_settings)
            elif file_extension in ['.txt']:
                return self._print_text(file_path, print_settings)
            else:
                self.logger.warning(f"نوع الملف غير مدعوم للطباعة: {file_extension}")
                return False
                
        except Exception as e:
            self.logger.error(f"خطأ في طباعة الوثيقة: {e}")
            return False
    
    def _print_image(self, image_path: Path, print_settings: Optional[Dict[str, Any]]) -> bool:
        """Print image file"""
        try:
            # Create printer
            printer = QPrinter(QPrinter.HighResolution)
            
            # Show print dialog
            print_dialog = QPrintDialog(printer)
            if print_dialog.exec() != QPrintDialog.Accepted:
                return False
            
            # Load and print image
            pixmap = QPixmap(str(image_path))
            if pixmap.isNull():
                self.logger.error("فشل في تحميل الصورة")
                return False
            
            painter = QPainter(printer)
            
            # Scale image to fit page
            page_rect = printer.pageRect(QPrinter.DevicePixel)
            scaled_pixmap = pixmap.scaled(
                page_rect.size().toSize(),
                aspectRatioMode=1,  # KeepAspectRatio
                transformMode=1     # SmoothTransformation
            )
            
            # Center image on page
            x = (page_rect.width() - scaled_pixmap.width()) / 2
            y = (page_rect.height() - scaled_pixmap.height()) / 2
            
            painter.drawPixmap(int(x), int(y), scaled_pixmap)
            painter.end()
            
            self.logger.info(f"تم طباعة الصورة: {image_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في طباعة الصورة: {e}")
            return False
    
    def _print_pdf(self, pdf_path: Path, print_settings: Optional[Dict[str, Any]]) -> bool:
        """Print PDF file"""
        try:
            # For PDF printing, we would typically use a PDF library
            # For now, show a message that PDF printing requires external viewer
            QMessageBox.information(
                None,
                "طباعة PDF",
                f"لطباعة ملف PDF، يرجى فتحه في برنامج عرض PDF وطباعته من هناك.\n"
                f"مسار الملف: {pdf_path}"
            )
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في طباعة PDF: {e}")
            return False
    
    def _print_text(self, text_path: Path, print_settings: Optional[Dict[str, Any]]) -> bool:
        """Print text file"""
        try:
            # Create printer
            printer = QPrinter(QPrinter.HighResolution)
            
            # Show print dialog
            print_dialog = QPrintDialog(printer)
            if print_dialog.exec() != QPrintDialog.Accepted:
                return False
            
            # Read text file
            with open(text_path, 'r', encoding='utf-8') as f:
                text_content = f.read()
            
            # Create text document
            document = QTextDocument()
            document.setPlainText(text_content)
            
            # Print document
            document.print(printer)
            
            self.logger.info(f"تم طباعة النص: {text_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في طباعة النص: {e}")
            return False
    
    def create_document_report(self, documents: List[Dict[str, Any]], 
                             report_path: str, include_thumbnails: bool = True) -> bool:
        """
        Create a report of documents
        
        Args:
            documents: List of documents
            report_path: Path to save report
            include_thumbnails: Include thumbnails in report
            
        Returns:
            True if successful
        """
        try:
            report_lines = [
                "تقرير الوثائق - Documents Report",
                "=" * 50,
                f"تاريخ التقرير - Report Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                f"عدد الوثائق - Total Documents: {len(documents)}",
                "",
                "تفاصيل الوثائق - Document Details:",
                "-" * 50
            ]
            
            for i, doc in enumerate(documents, 1):
                report_lines.extend([
                    f"\n{i}. {doc.get('title', 'Untitled')}",
                    f"   النوع - Type: {doc.get('file_type', 'Unknown')}",
                    f"   الحجم - Size: {self._format_file_size(doc.get('file_size', 0))}",
                    f"   التاريخ - Date: {doc.get('created_at', 'N/A')}",
                    f"   الوصف - Description: {doc.get('description', 'N/A')[:100]}..."
                ])
            
            # Save report
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write("\n".join(report_lines))
            
            self.logger.info(f"تم إنشاء تقرير الوثائق: {report_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء التقرير: {e}")
            return False
