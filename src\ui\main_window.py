"""
Main Window for Electronic Archive System
النافذة الرئيسية لنظام الأرشفة الإلكترونية

Main application window with document management interface
"""

import sys
from typing import Optional
from pathlib import Path

from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QSplitter,
    QTreeWidget, QTreeWidgetItem, QTableWidget, QTableWidgetItem,
    QMenuBar, QMenu, QToolBar, QStatusBar, QLabel, QPushButton,
    QLineEdit, QComboBox, QMessageBox, QFileDialog, QProgressBar,
    QTabWidget, QTextEdit, QGroupBox, QGridLayout
)
from PySide6.QtCore import Qt, QTimer, Signal, QThread, pyqtSignal
from PySide6.QtGui import QAction, QIcon, QPixmap, QFont

from ..core.config import Config
from ..core.database import DatabaseManager
from ..models import User, Category, Document


class MainWindow(QMainWindow):
    """
    Main application window
    النافذة الرئيسية للتطبيق
    """
    
    def __init__(self, config: Config, db_manager: DatabaseManager):
        """
        Initialize main window
        
        Args:
            config: Configuration manager
            db_manager: Database manager
        """
        super().__init__()
        
        self.config = config
        self.db_manager = db_manager
        self.current_user: Optional[User] = None
        
        self.setup_ui()
        self.setup_connections()
        self.load_initial_data()
        
        # Set window properties
        self.setWindowTitle("نظام الأرشفة الإلكترونية - Electronic Archive System")
        self.setMinimumSize(1000, 700)
        self.resize(
            self.config.getint('APPLICATION', 'window_width', 1200),
            self.config.getint('APPLICATION', 'window_height', 800)
        )
        
        # Center window on screen
        self.center_on_screen()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create main layout
        main_layout = QHBoxLayout(central_widget)
        
        # Create splitter for resizable panels
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # Left panel - Categories and navigation
        self.setup_left_panel(splitter)
        
        # Right panel - Document list and details
        self.setup_right_panel(splitter)
        
        # Set splitter proportions
        splitter.setSizes([300, 900])
        
        # Setup menu bar
        self.setup_menu_bar()
        
        # Setup toolbar
        self.setup_toolbar()
        
        # Setup status bar
        self.setup_status_bar()
    
    def setup_left_panel(self, parent):
        """إعداد اللوحة اليسرى"""
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        
        # Search section
        search_group = QGroupBox("البحث")
        search_layout = QVBoxLayout(search_group)
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("البحث في الوثائق...")
        search_layout.addWidget(self.search_input)
        
        search_button = QPushButton("بحث")
        search_layout.addWidget(search_button)
        
        left_layout.addWidget(search_group)
        
        # Categories tree
        categories_group = QGroupBox("الفئات")
        categories_layout = QVBoxLayout(categories_group)
        
        self.categories_tree = QTreeWidget()
        self.categories_tree.setHeaderLabel("الفئات")
        self.categories_tree.setRightToLeft(True)
        categories_layout.addWidget(self.categories_tree)
        
        # Category management buttons
        category_buttons_layout = QHBoxLayout()
        
        self.add_category_btn = QPushButton("إضافة فئة")
        self.edit_category_btn = QPushButton("تعديل")
        self.delete_category_btn = QPushButton("حذف")
        
        category_buttons_layout.addWidget(self.add_category_btn)
        category_buttons_layout.addWidget(self.edit_category_btn)
        category_buttons_layout.addWidget(self.delete_category_btn)
        
        categories_layout.addLayout(category_buttons_layout)
        left_layout.addWidget(categories_group)
        
        # Quick filters
        filters_group = QGroupBox("المرشحات السريعة")
        filters_layout = QVBoxLayout(filters_group)
        
        self.recent_docs_btn = QPushButton("الوثائق الحديثة")
        self.my_docs_btn = QPushButton("وثائقي")
        self.favorites_btn = QPushButton("المفضلة")
        
        filters_layout.addWidget(self.recent_docs_btn)
        filters_layout.addWidget(self.my_docs_btn)
        filters_layout.addWidget(self.favorites_btn)
        
        left_layout.addWidget(filters_group)
        
        # Add stretch to push everything to top
        left_layout.addStretch()
        
        parent.addWidget(left_widget)
    
    def setup_right_panel(self, parent):
        """إعداد اللوحة اليمنى"""
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        
        # Document management toolbar
        doc_toolbar_layout = QHBoxLayout()
        
        self.add_document_btn = QPushButton("إضافة وثيقة")
        self.scan_document_btn = QPushButton("مسح ضوئي")
        self.import_folder_btn = QPushButton("استيراد مجلد")
        
        doc_toolbar_layout.addWidget(self.add_document_btn)
        doc_toolbar_layout.addWidget(self.scan_document_btn)
        doc_toolbar_layout.addWidget(self.import_folder_btn)
        doc_toolbar_layout.addStretch()
        
        # View options
        self.view_combo = QComboBox()
        self.view_combo.addItems(["عرض قائمة", "عرض شبكي", "عرض تفصيلي"])
        doc_toolbar_layout.addWidget(QLabel("العرض:"))
        doc_toolbar_layout.addWidget(self.view_combo)
        
        right_layout.addLayout(doc_toolbar_layout)
        
        # Documents table
        self.documents_table = QTableWidget()
        self.setup_documents_table()
        right_layout.addWidget(self.documents_table)
        
        # Document details panel (bottom)
        details_splitter = QSplitter(Qt.Vertical)
        details_splitter.addWidget(self.documents_table)
        
        # Document preview/details
        self.setup_document_details(details_splitter)
        
        right_layout.addWidget(details_splitter)
        details_splitter.setSizes([500, 200])
        
        parent.addWidget(right_widget)
    
    def setup_documents_table(self):
        """إعداد جدول الوثائق"""
        headers = ["العنوان", "النوع", "الحجم", "الفئة", "تاريخ الإنشاء", "المنشئ"]
        self.documents_table.setColumnCount(len(headers))
        self.documents_table.setHorizontalHeaderLabels(headers)
        
        # Set table properties
        self.documents_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.documents_table.setAlternatingRowColors(True)
        self.documents_table.setSortingEnabled(True)
        
        # Set column widths
        self.documents_table.setColumnWidth(0, 250)  # Title
        self.documents_table.setColumnWidth(1, 80)   # Type
        self.documents_table.setColumnWidth(2, 80)   # Size
        self.documents_table.setColumnWidth(3, 150)  # Category
        self.documents_table.setColumnWidth(4, 120)  # Date
        self.documents_table.setColumnWidth(5, 100)  # Creator
    
    def setup_document_details(self, parent):
        """إعداد لوحة تفاصيل الوثيقة"""
        details_widget = QTabWidget()
        
        # Preview tab
        preview_tab = QWidget()
        preview_layout = QVBoxLayout(preview_tab)
        
        self.preview_label = QLabel("اختر وثيقة لعرض المعاينة")
        self.preview_label.setAlignment(Qt.AlignCenter)
        self.preview_label.setStyleSheet("border: 1px solid gray; background-color: #f0f0f0;")
        self.preview_label.setMinimumHeight(150)
        
        preview_layout.addWidget(self.preview_label)
        details_widget.addTab(preview_tab, "المعاينة")
        
        # Properties tab
        properties_tab = QWidget()
        properties_layout = QGridLayout(properties_tab)
        
        # Document properties
        properties_layout.addWidget(QLabel("العنوان:"), 0, 0)
        self.title_label = QLabel("-")
        properties_layout.addWidget(self.title_label, 0, 1)
        
        properties_layout.addWidget(QLabel("الوصف:"), 1, 0)
        self.description_text = QTextEdit()
        self.description_text.setMaximumHeight(60)
        self.description_text.setReadOnly(True)
        properties_layout.addWidget(self.description_text, 1, 1)
        
        properties_layout.addWidget(QLabel("الفئة:"), 2, 0)
        self.category_label = QLabel("-")
        properties_layout.addWidget(self.category_label, 2, 1)
        
        properties_layout.addWidget(QLabel("الحجم:"), 3, 0)
        self.size_label = QLabel("-")
        properties_layout.addWidget(self.size_label, 3, 1)
        
        properties_layout.addWidget(QLabel("النوع:"), 4, 0)
        self.type_label = QLabel("-")
        properties_layout.addWidget(self.type_label, 4, 1)
        
        properties_layout.addWidget(QLabel("تاريخ الإنشاء:"), 5, 0)
        self.created_date_label = QLabel("-")
        properties_layout.addWidget(self.created_date_label, 5, 1)
        
        properties_layout.setRowStretch(6, 1)  # Add stretch
        
        details_widget.addTab(properties_tab, "الخصائص")
        
        parent.addWidget(details_widget)
    
    def setup_menu_bar(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu("ملف")
        
        new_doc_action = QAction("وثيقة جديدة", self)
        new_doc_action.setShortcut("Ctrl+N")
        file_menu.addAction(new_doc_action)
        
        import_action = QAction("استيراد", self)
        import_action.setShortcut("Ctrl+I")
        file_menu.addAction(import_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction("خروج", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Edit menu
        edit_menu = menubar.addMenu("تحرير")
        
        # View menu
        view_menu = menubar.addMenu("عرض")
        
        # Tools menu
        tools_menu = menubar.addMenu("أدوات")
        
        scan_action = QAction("مسح ضوئي", self)
        tools_menu.addAction(scan_action)
        
        # Help menu
        help_menu = menubar.addMenu("مساعدة")
        
        about_action = QAction("حول البرنامج", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        
        # Add document action
        add_doc_action = QAction("إضافة وثيقة", self)
        toolbar.addAction(add_doc_action)
        
        # Scan action
        scan_action = QAction("مسح ضوئي", self)
        toolbar.addAction(scan_action)
        
        toolbar.addSeparator()
        
        # Search action
        search_action = QAction("بحث", self)
        toolbar.addAction(search_action)
    
    def setup_status_bar(self):
        """إعداد شريط الحالة"""
        self.status_bar = self.statusBar()
        
        # Status label
        self.status_label = QLabel("جاهز")
        self.status_bar.addWidget(self.status_label)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.status_bar.addPermanentWidget(self.progress_bar)
        
        # User info
        self.user_label = QLabel("غير مسجل الدخول")
        self.status_bar.addPermanentWidget(self.user_label)
    
    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        # Categories tree selection
        self.categories_tree.itemSelectionChanged.connect(self.on_category_selected)
        
        # Documents table selection
        self.documents_table.itemSelectionChanged.connect(self.on_document_selected)
        
        # Search
        self.search_input.returnPressed.connect(self.perform_search)
        
        # Category management
        self.add_category_btn.clicked.connect(self.add_category)
        self.edit_category_btn.clicked.connect(self.edit_category)
        self.delete_category_btn.clicked.connect(self.delete_category)
        
        # Document management
        self.add_document_btn.clicked.connect(self.add_document)
        self.scan_document_btn.clicked.connect(self.scan_document)
        self.import_folder_btn.clicked.connect(self.import_folder)
        
        # Quick filters
        self.recent_docs_btn.clicked.connect(self.show_recent_documents)
        self.my_docs_btn.clicked.connect(self.show_my_documents)
        self.favorites_btn.clicked.connect(self.show_favorites)
    
    def center_on_screen(self):
        """توسيط النافذة على الشاشة"""
        screen = self.screen().availableGeometry()
        window = self.frameGeometry()
        window.moveCenter(screen.center())
        self.move(window.topLeft())
    
    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        self.load_categories()
        self.load_recent_documents()
    
    def load_categories(self):
        """تحميل الفئات في الشجرة"""
        # This will be implemented with actual database data
        pass
    
    def load_recent_documents(self):
        """تحميل الوثائق الحديثة"""
        # This will be implemented with actual database data
        pass
    
    # Event handlers (to be implemented)
    def on_category_selected(self): pass
    def on_document_selected(self): pass
    def perform_search(self): pass
    def add_category(self): pass
    def edit_category(self): pass
    def delete_category(self): pass
    def add_document(self): pass
    def scan_document(self): pass
    def import_folder(self): pass
    def show_recent_documents(self): pass
    def show_my_documents(self): pass
    def show_favorites(self): pass
    
    def show_about(self):
        """عرض معلومات البرنامج"""
        QMessageBox.about(
            self,
            "حول البرنامج",
            "نظام الأرشفة الإلكترونية\n"
            "الإصدار 1.0.0\n\n"
            "نظام شامل لإدارة الوثائق والأرشفة الإلكترونية"
        )
