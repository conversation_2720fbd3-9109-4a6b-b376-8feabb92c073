#!/usr/bin/env python3
"""
Simple Electronic Archive System - Main Entry Point
نظام الأرشفة الإلكترونية المبسط - نقطة الدخول الرئيسية

A simplified version using SQLite for easy testing and development
"""

import sys
import os
import logging
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import QTranslator, QLocale
from PySide6.QtGui import QIcon

from src.core.config import Config
from src.core.database_sqlite import DatabaseManagerSQLite
from src.ui.main_window import MainWindow
from src.ui.login_dialog import LoginDialog
from src.core.logger import setup_logging


def setup_application():
    """إعداد التطبيق الأساسي"""
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("Electronic Archive System (Simple)")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("Archive Solutions")
    
    return app


def check_dependencies():
    """فحص التبعيات المطلوبة"""
    try:
        import sqlalchemy
        from PySide6 import QtWidgets
        return True
    except ImportError as e:
        QMessageBox.critical(
            None,
            "خطأ في التبعيات",
            f"مكتبة مطلوبة غير موجودة: {e}\n"
            "يرجى تثبيت المتطلبات باستخدام:\n"
            "python -m pip install PySide6 sqlalchemy bcrypt python-dateutil"
        )
        return False


def create_initial_data(db_manager):
    """إنشاء البيانات الأولية"""
    try:
        from src.models import User, Category, UserRole
        
        with db_manager.get_session() as session:
            # Check if admin user exists
            admin_user = session.query(User).filter(User.username == "admin").first()
            if not admin_user:
                # Create admin user
                admin_user = User(
                    username="admin",
                    email="<EMAIL>",
                    full_name="مدير النظام",
                    role=UserRole.ADMIN
                )
                admin_user.set_password("Admin123!")
                session.add(admin_user)
                session.commit()
                print("تم إنشاء مستخدم المدير: admin / Admin123!")
            
            # Check if categories exist
            categories_count = session.query(Category).count()
            if categories_count == 0:
                # Create sample categories
                categories = [
                    Category(name="الوثائق الإدارية", description="الوثائق والمراسلات الإدارية", color="#0078D4"),
                    Category(name="الوثائق المالية", description="الفواتير والمستندات المالية", color="#107C10"),
                    Category(name="الوثائق القانونية", description="العقود والوثائق القانونية", color="#D13438"),
                ]
                
                for category in categories:
                    session.add(category)
                
                session.commit()
                print("تم إنشاء الفئات الأولية")
        
        return True
        
    except Exception as e:
        print(f"خطأ في إنشاء البيانات الأولية: {e}")
        return False


def main():
    """الدالة الرئيسية للتطبيق"""
    try:
        # Setup logging
        setup_logging()
        logger = logging.getLogger(__name__)
        logger.info("بدء تشغيل نظام الأرشفة الإلكترونية المبسط")
        
        # Check dependencies
        if not check_dependencies():
            return 1
        
        # Setup application
        app = setup_application()
        
        # Load configuration
        config = Config()
        
        # Initialize SQLite database
        db_manager = DatabaseManagerSQLite(config)
        
        print("الاتصال بقاعدة البيانات SQLite...")
        if not db_manager.initialize():
            QMessageBox.critical(
                None,
                "خطأ في قاعدة البيانات",
                "فشل في إنشاء قاعدة البيانات SQLite"
            )
            return 1
        
        print("تم الاتصال بقاعدة البيانات بنجاح")
        
        # Create initial data
        print("إنشاء البيانات الأولية...")
        create_initial_data(db_manager)
        
        # Show login dialog
        print("عرض نافذة تسجيل الدخول...")
        user = LoginDialog.get_user_login(db_manager)
        if not user:
            # User cancelled login
            print("تم إلغاء تسجيل الدخول")
            return 0
        
        print(f"تم تسجيل الدخول بنجاح: {user.username}")
        
        # Create and show main window
        main_window = MainWindow(config, db_manager)
        main_window.current_user = user
        main_window.user_label.setText(f"مرحباً، {user.full_name or user.username}")
        main_window.show()
        
        logger.info("تم تشغيل النظام بنجاح")
        
        # Run application
        return app.exec()
        
    except Exception as e:
        logger.error(f"خطأ في تشغيل التطبيق: {e}")
        QMessageBox.critical(
            None,
            "خطأ في النظام",
            f"حدث خطأ غير متوقع: {e}"
        )
        return 1


if __name__ == "__main__":
    print("=" * 60)
    print("نظام الأرشفة الإلكترونية المبسط")
    print("Electronic Archive System (Simple)")
    print("=" * 60)
    print("استخدام قاعدة بيانات SQLite للاختبار والتطوير")
    print("Using SQLite database for testing and development")
    print("=" * 60)
    
    sys.exit(main())
