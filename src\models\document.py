"""
Document Model for Electronic Archive System
نموذج الوثيقة لنظام الأرشفة الإلكترونية

Handles document storage, metadata, and file management
"""

from datetime import datetime, date
from typing import Optional, List, Dict, Any
from pathlib import Path
from sqlalchemy import Column, String, Integer, BigInteger, Date, Text, Boolean, ForeignKey
from sqlalchemy.orm import relationship, Session

from .base import BaseModel


class Document(BaseModel):
    """
    Document model for file storage and metadata
    نموذج الوثيقة لتخزين الملفات والبيانات الوصفية
    """
    __tablename__ = "documents"
    
    title = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)
    file_name = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(BigInteger, nullable=False)
    file_type = Column(String(50), nullable=False, index=True)
    mime_type = Column(String(100), nullable=True)
    
    # Foreign keys
    category_id = Column(Integer, ForeignKey('categories.id'), nullable=True, index=True)
    created_by = Column(Integer, ForeignKey('users.id'), nullable=False, index=True)
    
    # Document metadata
    document_date = Column(Date, nullable=True, index=True)
    tags = Column(String(500), nullable=True)
    
    # File processing
    is_encrypted = Column(Boolean, default=False)
    thumbnail_path = Column(String(500), nullable=True)
    full_text_content = Column(Text, nullable=True)  # For search indexing
    
    # Relationships
    category = relationship("Category", back_populates="documents")
    created_by_user = relationship("User", back_populates="documents")
    attachments = relationship("DocumentAttachment", back_populates="document", lazy="dynamic")
    
    @property
    def file_extension(self) -> str:
        """
        Get file extension
        
        Returns:
            File extension (e.g., 'pdf', 'docx')
        """
        return Path(self.file_name).suffix.lower().lstrip('.')
    
    @property
    def file_size_mb(self) -> float:
        """
        Get file size in megabytes
        
        Returns:
            File size in MB
        """
        return round(self.file_size / (1024 * 1024), 2)
    
    @property
    def tags_list(self) -> List[str]:
        """
        Get tags as list
        
        Returns:
            List of tags
        """
        if not self.tags:
            return []
        return [tag.strip() for tag in self.tags.split(',') if tag.strip()]
    
    @tags_list.setter
    def tags_list(self, tags: List[str]):
        """
        Set tags from list
        
        Args:
            tags: List of tags
        """
        self.tags = ', '.join(tags) if tags else None
    
    @property
    def is_image(self) -> bool:
        """Check if document is an image"""
        image_extensions = {'jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'webp'}
        return self.file_extension in image_extensions
    
    @property
    def is_pdf(self) -> bool:
        """Check if document is a PDF"""
        return self.file_extension == 'pdf'
    
    @property
    def is_office_document(self) -> bool:
        """Check if document is an Office document"""
        office_extensions = {'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'}
        return self.file_extension in office_extensions
    
    @property
    def can_preview(self) -> bool:
        """Check if document can be previewed"""
        return self.is_image or self.is_pdf or bool(self.thumbnail_path)
    
    def add_tag(self, tag: str):
        """
        Add tag to document
        
        Args:
            tag: Tag to add
        """
        current_tags = self.tags_list
        if tag not in current_tags:
            current_tags.append(tag)
            self.tags_list = current_tags
    
    def remove_tag(self, tag: str):
        """
        Remove tag from document
        
        Args:
            tag: Tag to remove
        """
        current_tags = self.tags_list
        if tag in current_tags:
            current_tags.remove(tag)
            self.tags_list = current_tags
    
    def get_file_path(self) -> Path:
        """
        Get full file path as Path object
        
        Returns:
            Path object for the file
        """
        return Path(self.file_path)
    
    def file_exists(self) -> bool:
        """
        Check if physical file exists
        
        Returns:
            True if file exists
        """
        return self.get_file_path().exists()
    
    def get_thumbnail_path(self) -> Optional[Path]:
        """
        Get thumbnail path as Path object
        
        Returns:
            Path object for thumbnail or None
        """
        if self.thumbnail_path:
            return Path(self.thumbnail_path)
        return None
    
    def thumbnail_exists(self) -> bool:
        """
        Check if thumbnail exists
        
        Returns:
            True if thumbnail exists
        """
        thumbnail_path = self.get_thumbnail_path()
        return thumbnail_path and thumbnail_path.exists()
    
    @classmethod
    def search(cls, session: Session, query: str, category_id: Optional[int] = None,
               file_type: Optional[str] = None, date_from: Optional[date] = None,
               date_to: Optional[date] = None, created_by: Optional[int] = None,
               limit: int = 100) -> List['Document']:
        """
        Search documents with various filters
        
        Args:
            session: Database session
            query: Search query
            category_id: Filter by category
            file_type: Filter by file type
            date_from: Filter by date range (from)
            date_to: Filter by date range (to)
            created_by: Filter by creator
            limit: Maximum results
            
        Returns:
            List of matching documents
        """
        db_query = session.query(cls).filter(cls.is_active == True)
        
        # Text search in title, description, and full text content
        if query:
            search_filter = (
                cls.title.contains(query) |
                cls.description.contains(query) |
                cls.full_text_content.contains(query) |
                cls.tags.contains(query)
            )
            db_query = db_query.filter(search_filter)
        
        # Category filter
        if category_id:
            db_query = db_query.filter(cls.category_id == category_id)
        
        # File type filter
        if file_type:
            db_query = db_query.filter(cls.file_type == file_type)
        
        # Date range filter
        if date_from:
            db_query = db_query.filter(cls.document_date >= date_from)
        if date_to:
            db_query = db_query.filter(cls.document_date <= date_to)
        
        # Creator filter
        if created_by:
            db_query = db_query.filter(cls.created_by == created_by)
        
        return db_query.order_by(cls.created_at.desc()).limit(limit).all()
    
    @classmethod
    def get_by_category(cls, session: Session, category_id: int, include_subcategories: bool = False) -> List['Document']:
        """
        Get documents by category
        
        Args:
            session: Database session
            category_id: Category ID
            include_subcategories: Include documents from subcategories
            
        Returns:
            List of documents
        """
        if include_subcategories:
            # Get category and all its subcategories
            from .category import Category
            category = session.query(Category).get(category_id)
            if not category:
                return []
            
            all_categories = [category] + category.get_all_children()
            category_ids = [cat.id for cat in all_categories]
            
            return session.query(cls).filter(
                cls.category_id.in_(category_ids),
                cls.is_active == True
            ).order_by(cls.created_at.desc()).all()
        else:
            return session.query(cls).filter(
                cls.category_id == category_id,
                cls.is_active == True
            ).order_by(cls.created_at.desc()).all()
    
    @classmethod
    def get_recent(cls, session: Session, limit: int = 10, user_id: Optional[int] = None) -> List['Document']:
        """
        Get recent documents
        
        Args:
            session: Database session
            limit: Maximum number of documents
            user_id: Filter by user (optional)
            
        Returns:
            List of recent documents
        """
        query = session.query(cls).filter(cls.is_active == True)
        
        if user_id:
            query = query.filter(cls.created_by == user_id)
        
        return query.order_by(cls.created_at.desc()).limit(limit).all()
    
    def to_dict(self, include_content: bool = False) -> Dict[str, Any]:
        """
        Convert document to dictionary
        
        Args:
            include_content: Include full text content
            
        Returns:
            Dictionary representation
        """
        data = super().to_dict()
        data.update({
            'file_extension': self.file_extension,
            'file_size_mb': self.file_size_mb,
            'tags_list': self.tags_list,
            'is_image': self.is_image,
            'is_pdf': self.is_pdf,
            'is_office_document': self.is_office_document,
            'can_preview': self.can_preview,
            'file_exists': self.file_exists(),
            'thumbnail_exists': self.thumbnail_exists()
        })
        
        if not include_content:
            data.pop('full_text_content', None)
        
        return data
    
    def __repr__(self):
        return f"<Document(id={self.id}, title='{self.title}', file_type='{self.file_type}')>"
