"""
Configuration Manager for Electronic Archive System
مدير التكوين لنظام الأرشفة الإلكترونية

Handles loading and managing application configuration from config.ini
"""

import configparser
import os
from pathlib import Path
from typing import Dict, Any, Optional
import logging


class Config:
    """مدير التكوين للتطبيق"""
    
    def __init__(self, config_file: str = "config.ini"):
        """
        Initialize configuration manager
        
        Args:
            config_file: Path to configuration file
        """
        self.config_file = config_file
        self.config = configparser.ConfigParser()
        self._load_config()
        self.logger = logging.getLogger(__name__)
    
    def _load_config(self):
        """تحميل ملف التكوين"""
        try:
            if os.path.exists(self.config_file):
                self.config.read(self.config_file, encoding='utf-8')
            else:
                self._create_default_config()
        except Exception as e:
            raise Exception(f"خطأ في تحميل ملف التكوين: {e}")
    
    def _create_default_config(self):
        """إنشاء ملف تكوين افتراضي"""
        # Database configuration
        self.config['DATABASE'] = {
            'server': 'localhost',
            'database': 'ArchiveDB',
            'driver': 'ODBC Driver 17 for SQL Server',
            'trusted_connection': 'yes',
            'connection_timeout': '30',
            'command_timeout': '30'
        }
        
        # Application configuration
        self.config['APPLICATION'] = {
            'app_name': 'Electronic Archive System',
            'version': '1.0.0',
            'language': 'ar',
            'theme': 'light',
            'window_width': '1200',
            'window_height': '800',
            'max_file_size': '100MB',
            'supported_formats': 'pdf,doc,docx,xls,xlsx,ppt,pptx,txt,jpg,jpeg,png,tiff,bmp'
        }
        
        # Storage configuration
        self.config['STORAGE'] = {
            'documents_path': './documents',
            'thumbnails_path': './thumbnails',
            'temp_path': './temp',
            'backup_path': './backup',
            'max_storage_size': '10GB'
        }
        
        # Security configuration
        self.config['SECURITY'] = {
            'encryption_enabled': 'true',
            'password_policy': 'strong',
            'session_timeout': '30',
            'audit_enabled': 'true',
            'backup_encryption': 'true'
        }
        
        # Save default configuration
        self.save_config()
    
    def get(self, section: str, key: str, fallback: Any = None) -> str:
        """
        Get configuration value
        
        Args:
            section: Configuration section
            key: Configuration key
            fallback: Default value if key not found
            
        Returns:
            Configuration value
        """
        try:
            return self.config.get(section, key, fallback=fallback)
        except (configparser.NoSectionError, configparser.NoOptionError):
            return fallback
    
    def getint(self, section: str, key: str, fallback: int = 0) -> int:
        """Get integer configuration value"""
        try:
            return self.config.getint(section, key, fallback=fallback)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
            return fallback
    
    def getboolean(self, section: str, key: str, fallback: bool = False) -> bool:
        """Get boolean configuration value"""
        try:
            return self.config.getboolean(section, key, fallback=fallback)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
            return fallback
    
    def set(self, section: str, key: str, value: str):
        """
        Set configuration value
        
        Args:
            section: Configuration section
            key: Configuration key
            value: Configuration value
        """
        if not self.config.has_section(section):
            self.config.add_section(section)
        
        self.config.set(section, key, str(value))
    
    def save_config(self):
        """حفظ ملف التكوين"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                self.config.write(f)
        except Exception as e:
            self.logger.error(f"خطأ في حفظ ملف التكوين: {e}")
            raise
    
    @property
    def database_config(self) -> Dict[str, str]:
        """Get database configuration"""
        return {
            'server': self.get('DATABASE', 'server', 'localhost'),
            'database': self.get('DATABASE', 'database', 'ArchiveDB'),
            'driver': self.get('DATABASE', 'driver', 'ODBC Driver 17 for SQL Server'),
            'trusted_connection': self.get('DATABASE', 'trusted_connection', 'yes'),
            'connection_timeout': self.getint('DATABASE', 'connection_timeout', 30),
            'command_timeout': self.getint('DATABASE', 'command_timeout', 30)
        }
    
    @property
    def storage_paths(self) -> Dict[str, Path]:
        """Get storage paths"""
        return {
            'documents': Path(self.get('STORAGE', 'documents_path', './documents')),
            'thumbnails': Path(self.get('STORAGE', 'thumbnails_path', './thumbnails')),
            'temp': Path(self.get('STORAGE', 'temp_path', './temp')),
            'backup': Path(self.get('STORAGE', 'backup_path', './backup'))
        }
    
    def ensure_directories(self):
        """التأكد من وجود المجلدات المطلوبة"""
        for path in self.storage_paths.values():
            path.mkdir(parents=True, exist_ok=True)
        
        # Create logs directory
        logs_path = Path('./logs')
        logs_path.mkdir(parents=True, exist_ok=True)
