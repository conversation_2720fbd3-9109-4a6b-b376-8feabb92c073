"""
Helper utilities for Electronic Archive System
الأدوات المساعدة لنظام الأرشفة الإلكترونية

Common utility functions and helpers
"""

import os
import re
import hashlib
from typing import Any, Dict, List, Optional, Union
from datetime import datetime, date
from pathlib import Path
import mimetypes


def format_file_size(size_bytes: int) -> str:
    """
    Format file size in human readable format
    
    Args:
        size_bytes: Size in bytes
        
    Returns:
        Formatted size string
    """
    if size_bytes == 0:
        return "0 بايت"
    
    size_names = ["بايت", "كيلوبايت", "ميجابايت", "جيجابايت", "تيرابايت"]
    i = 0
    
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"


def format_date_arabic(date_obj: Union[datetime, date]) -> str:
    """
    Format date in Arabic format
    
    Args:
        date_obj: Date or datetime object
        
    Returns:
        Formatted date string in Arabic
    """
    if isinstance(date_obj, datetime):
        return date_obj.strftime("%d/%m/%Y %H:%M")
    elif isinstance(date_obj, date):
        return date_obj.strftime("%d/%m/%Y")
    else:
        return str(date_obj)


def sanitize_filename(filename: str) -> str:
    """
    Sanitize filename for safe storage
    
    Args:
        filename: Original filename
        
    Returns:
        Sanitized filename
    """
    # Remove or replace invalid characters
    invalid_chars = r'[<>:"/\\|?*]'
    sanitized = re.sub(invalid_chars, '_', filename)
    
    # Remove leading/trailing spaces and dots
    sanitized = sanitized.strip(' .')
    
    # Limit length
    if len(sanitized) > 255:
        name, ext = os.path.splitext(sanitized)
        max_name_length = 255 - len(ext)
        sanitized = name[:max_name_length] + ext
    
    return sanitized


def validate_email(email: str) -> bool:
    """
    Validate email address format
    
    Args:
        email: Email address to validate
        
    Returns:
        True if valid email format
    """
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None


def generate_unique_filename(directory: Path, base_name: str, extension: str) -> str:
    """
    Generate unique filename in directory
    
    Args:
        directory: Target directory
        base_name: Base filename without extension
        extension: File extension (with or without dot)
        
    Returns:
        Unique filename
    """
    if not extension.startswith('.'):
        extension = '.' + extension
    
    filename = base_name + extension
    counter = 1
    
    while (directory / filename).exists():
        filename = f"{base_name}_{counter}{extension}"
        counter += 1
    
    return filename


def calculate_md5(file_path: Path) -> str:
    """
    Calculate MD5 hash of file
    
    Args:
        file_path: Path to file
        
    Returns:
        MD5 hash string
    """
    hash_md5 = hashlib.md5()
    
    try:
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    except Exception:
        return ""


def get_mime_type(file_path: Path) -> Optional[str]:
    """
    Get MIME type of file
    
    Args:
        file_path: Path to file
        
    Returns:
        MIME type string or None
    """
    mime_type, _ = mimetypes.guess_type(str(file_path))
    return mime_type


def is_image_file(file_path: Path) -> bool:
    """
    Check if file is an image
    
    Args:
        file_path: Path to file
        
    Returns:
        True if file is an image
    """
    image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp'}
    return file_path.suffix.lower() in image_extensions


def is_document_file(file_path: Path) -> bool:
    """
    Check if file is a document
    
    Args:
        file_path: Path to file
        
    Returns:
        True if file is a document
    """
    document_extensions = {'.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt'}
    return file_path.suffix.lower() in document_extensions


def clean_text(text: str) -> str:
    """
    Clean text for search indexing
    
    Args:
        text: Raw text
        
    Returns:
        Cleaned text
    """
    if not text:
        return ""
    
    # Remove extra whitespace
    text = re.sub(r'\s+', ' ', text)
    
    # Remove special characters but keep Arabic and English letters
    text = re.sub(r'[^\w\s\u0600-\u06FF]', ' ', text)
    
    # Strip and return
    return text.strip()


def parse_tags(tags_string: str) -> List[str]:
    """
    Parse tags string into list
    
    Args:
        tags_string: Comma-separated tags string
        
    Returns:
        List of cleaned tags
    """
    if not tags_string:
        return []
    
    tags = [tag.strip() for tag in tags_string.split(',')]
    return [tag for tag in tags if tag]


def format_tags(tags_list: List[str]) -> str:
    """
    Format tags list into string
    
    Args:
        tags_list: List of tags
        
    Returns:
        Comma-separated tags string
    """
    if not tags_list:
        return ""
    
    return ', '.join(tags_list)


def truncate_text(text: str, max_length: int = 100, suffix: str = "...") -> str:
    """
    Truncate text to specified length
    
    Args:
        text: Text to truncate
        max_length: Maximum length
        suffix: Suffix to add if truncated
        
    Returns:
        Truncated text
    """
    if not text or len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix


def safe_int(value: Any, default: int = 0) -> int:
    """
    Safely convert value to integer
    
    Args:
        value: Value to convert
        default: Default value if conversion fails
        
    Returns:
        Integer value
    """
    try:
        return int(value)
    except (ValueError, TypeError):
        return default


def safe_float(value: Any, default: float = 0.0) -> float:
    """
    Safely convert value to float
    
    Args:
        value: Value to convert
        default: Default value if conversion fails
        
    Returns:
        Float value
    """
    try:
        return float(value)
    except (ValueError, TypeError):
        return default


def dict_to_query_string(params: Dict[str, Any]) -> str:
    """
    Convert dictionary to query string
    
    Args:
        params: Dictionary of parameters
        
    Returns:
        Query string
    """
    parts = []
    for key, value in params.items():
        if value is not None:
            parts.append(f"{key}={value}")
    
    return "&".join(parts)


def ensure_directory(path: Path) -> bool:
    """
    Ensure directory exists, create if necessary
    
    Args:
        path: Directory path
        
    Returns:
        True if directory exists or was created successfully
    """
    try:
        path.mkdir(parents=True, exist_ok=True)
        return True
    except Exception:
        return False


def get_file_icon_name(file_path: Path) -> str:
    """
    Get icon name for file type
    
    Args:
        file_path: Path to file
        
    Returns:
        Icon name string
    """
    extension = file_path.suffix.lower()
    
    icon_map = {
        '.pdf': 'file-pdf',
        '.doc': 'file-word',
        '.docx': 'file-word',
        '.xls': 'file-excel',
        '.xlsx': 'file-excel',
        '.ppt': 'file-powerpoint',
        '.pptx': 'file-powerpoint',
        '.txt': 'file-text',
        '.jpg': 'file-image',
        '.jpeg': 'file-image',
        '.png': 'file-image',
        '.gif': 'file-image',
        '.bmp': 'file-image',
        '.tiff': 'file-image',
        '.zip': 'file-archive',
        '.rar': 'file-archive',
        '.7z': 'file-archive',
    }
    
    return icon_map.get(extension, 'file')


def validate_password_strength(password: str) -> Dict[str, Any]:
    """
    Validate password strength
    
    Args:
        password: Password to validate
        
    Returns:
        Dictionary with validation results
    """
    result = {
        'is_valid': True,
        'score': 0,
        'errors': []
    }
    
    if len(password) < 8:
        result['errors'].append('كلمة المرور يجب أن تكون 8 أحرف على الأقل')
        result['is_valid'] = False
    else:
        result['score'] += 1
    
    if not re.search(r'[A-Z]', password):
        result['errors'].append('كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل')
        result['is_valid'] = False
    else:
        result['score'] += 1
    
    if not re.search(r'[a-z]', password):
        result['errors'].append('كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل')
        result['is_valid'] = False
    else:
        result['score'] += 1
    
    if not re.search(r'\d', password):
        result['errors'].append('كلمة المرور يجب أن تحتوي على رقم واحد على الأقل')
        result['is_valid'] = False
    else:
        result['score'] += 1
    
    if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
        result['errors'].append('كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل')
        result['is_valid'] = False
    else:
        result['score'] += 1
    
    return result
