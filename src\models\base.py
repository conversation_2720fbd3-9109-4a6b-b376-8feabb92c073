"""
Base Model for Electronic Archive System
النموذج الأساسي لنظام الأرشفة الإلكترونية

Contains base model class and common database functionality
"""

from datetime import datetime
from typing import Optional, Dict, Any
from sqlalchemy import Column, Integer, DateTime, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import Session


# Create base class for all models
Base = declarative_base()


class BaseModel(Base):
    """
    Base model class with common fields and methods
    فئة النموذج الأساسي مع الحقول والطرق المشتركة
    """
    __abstract__ = True
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert model instance to dictionary
        
        Returns:
            Dictionary representation of the model
        """
        result = {}
        for column in self.__table__.columns:
            value = getattr(self, column.name)
            if isinstance(value, datetime):
                value = value.isoformat()
            result[column.name] = value
        return result
    
    def update_from_dict(self, data: Dict[str, Any], exclude: Optional[list] = None):
        """
        Update model instance from dictionary
        
        Args:
            data: Dictionary with new values
            exclude: List of fields to exclude from update
        """
        if exclude is None:
            exclude = ['id', 'created_at']
        
        for key, value in data.items():
            if key not in exclude and hasattr(self, key):
                setattr(self, key, value)
        
        self.updated_at = datetime.utcnow()
    
    @classmethod
    def create(cls, session: Session, **kwargs):
        """
        Create new instance and save to database
        
        Args:
            session: Database session
            **kwargs: Model field values
            
        Returns:
            Created model instance
        """
        instance = cls(**kwargs)
        session.add(instance)
        session.commit()
        session.refresh(instance)
        return instance
    
    def save(self, session: Session):
        """
        Save instance to database
        
        Args:
            session: Database session
        """
        session.add(self)
        session.commit()
        session.refresh(self)
    
    def delete(self, session: Session, soft_delete: bool = True):
        """
        Delete instance from database
        
        Args:
            session: Database session
            soft_delete: If True, mark as inactive instead of deleting
        """
        if soft_delete:
            self.is_active = False
            self.updated_at = datetime.utcnow()
            session.commit()
        else:
            session.delete(self)
            session.commit()
    
    @classmethod
    def get_by_id(cls, session: Session, id: int):
        """
        Get instance by ID
        
        Args:
            session: Database session
            id: Instance ID
            
        Returns:
            Model instance or None
        """
        return session.query(cls).filter(cls.id == id, cls.is_active == True).first()
    
    @classmethod
    def get_all(cls, session: Session, include_inactive: bool = False):
        """
        Get all instances
        
        Args:
            session: Database session
            include_inactive: Include inactive records
            
        Returns:
            List of model instances
        """
        query = session.query(cls)
        if not include_inactive:
            query = query.filter(cls.is_active == True)
        return query.all()
    
    @classmethod
    def count(cls, session: Session, include_inactive: bool = False) -> int:
        """
        Count instances
        
        Args:
            session: Database session
            include_inactive: Include inactive records
            
        Returns:
            Number of instances
        """
        query = session.query(cls)
        if not include_inactive:
            query = query.filter(cls.is_active == True)
        return query.count()
    
    def __repr__(self):
        """String representation of the model"""
        return f"<{self.__class__.__name__}(id={self.id})>"
