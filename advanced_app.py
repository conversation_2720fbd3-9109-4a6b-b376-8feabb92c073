#!/usr/bin/env python3
"""
Advanced Electronic Archive System
نظام الأرشفة الإلكترونية المتقدم

Full-featured application with all advanced capabilities
"""

import sys
import os
import logging
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PySide6.QtCore import QTimer, Qt
from PySide6.QtGui import QPixmap, QFont

from src.core.config import Config
from src.core.database_sqlite import DatabaseManagerSQLite
from src.core.logger import setup_logging
from src.ui.theme_manager import theme_manager, ThemeType
from src.services.search_fts5 import SearchServiceFTS5
from src.services.preview_service import PreviewService
from src.services.scanning_service import ScanningService
from src.services.export_service import ExportService
from src.services.trash_service import TrashService


class AdvancedArchiveApp:
    """
    Advanced Electronic Archive Application
    تطبيق الأرشفة الإلكترونية المتقدم
    """
    
    def __init__(self):
        """Initialize the advanced application"""
        self.app = None
        self.config = None
        self.db_manager = None
        self.services = {}
        self.main_window = None
        
        # Setup logging
        setup_logging()
        self.logger = logging.getLogger(__name__)
    
    def initialize(self) -> bool:
        """
        Initialize the application
        
        Returns:
            True if successful
        """
        try:
            # Create QApplication
            self.app = QApplication(sys.argv)
            
            # Set application properties
            self.app.setApplicationName("Electronic Archive System Advanced")
            self.app.setApplicationVersion("2.0.0")
            self.app.setOrganizationName("Archive Solutions")
            
            # Setup high-DPI support
            theme_manager.setup_high_dpi_support()
            
            # Show splash screen
            splash = self._create_splash_screen()
            splash.show()
            
            # Process events to show splash
            self.app.processEvents()
            
            # Load configuration
            splash.showMessage("تحميل التكوين... Loading configuration...", Qt.AlignBottom | Qt.AlignCenter)
            self.app.processEvents()
            
            self.config = Config()
            self.config.ensure_directories()
            
            # Initialize database
            splash.showMessage("تهيئة قاعدة البيانات... Initializing database...", Qt.AlignBottom | Qt.AlignCenter)
            self.app.processEvents()
            
            self.db_manager = DatabaseManagerSQLite(self.config)
            if not self.db_manager.initialize():
                splash.close()
                QMessageBox.critical(None, "خطأ", "فشل في تهيئة قاعدة البيانات")
                return False
            
            # Initialize services
            splash.showMessage("تهيئة الخدمات... Initializing services...", Qt.AlignBottom | Qt.AlignCenter)
            self.app.processEvents()
            
            self._initialize_services()
            
            # Setup theme
            splash.showMessage("تطبيق المظهر... Applying theme...", Qt.AlignBottom | Qt.AlignCenter)
            self.app.processEvents()
            
            theme_manager._apply_theme()
            
            # Create initial data if needed
            splash.showMessage("إعداد البيانات الأولية... Setting up initial data...", Qt.AlignBottom | Qt.AlignCenter)
            self.app.processEvents()
            
            self._create_initial_data()
            
            # Close splash screen
            splash.close()
            
            self.logger.info("تم تهيئة التطبيق المتقدم بنجاح")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في تهيئة التطبيق: {e}")
            if 'splash' in locals():
                splash.close()
            QMessageBox.critical(None, "خطأ", f"فشل في تهيئة التطبيق: {e}")
            return False
    
    def _create_splash_screen(self) -> QSplashScreen:
        """Create splash screen"""
        # Create a simple splash screen
        pixmap = QPixmap(400, 300)
        pixmap.fill(Qt.white)
        
        splash = QSplashScreen(pixmap)
        splash.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.SplashScreen)
        
        # Add text
        font = QFont()
        font.setPointSize(16)
        font.setBold(True)
        splash.setFont(font)
        
        return splash
    
    def _initialize_services(self):
        """Initialize all services"""
        try:
            # Search service with FTS5
            self.services['search'] = SearchServiceFTS5(self.db_manager)
            
            # Preview service
            self.services['preview'] = PreviewService(self.config)
            
            # Scanning service
            self.services['scanning'] = ScanningService(self.config)
            
            # Export service
            self.services['export'] = ExportService(self.config)
            
            # Trash service
            self.services['trash'] = TrashService(self.db_manager)
            
            self.logger.info("تم تهيئة جميع الخدمات بنجاح")
            
        except Exception as e:
            self.logger.error(f"خطأ في تهيئة الخدمات: {e}")
            raise
    
    def _create_initial_data(self):
        """Create initial data if needed"""
        try:
            from src.models import User, Category, UserRole
            
            with self.db_manager.get_session() as session:
                # Check if admin user exists
                admin_user = session.query(User).filter(User.username == "admin").first()
                if not admin_user:
                    # Create admin user
                    admin_user = User(
                        username="admin",
                        email="<EMAIL>",
                        full_name="مدير النظام",
                        role=UserRole.ADMIN
                    )
                    admin_user.set_password("Admin123!")
                    session.add(admin_user)
                    session.commit()
                    self.logger.info("تم إنشاء مستخدم المدير")
                
                # Check if categories exist
                categories_count = session.query(Category).count()
                if categories_count == 0:
                    # Create sample categories
                    categories = [
                        Category(name="الوثائق الإدارية", description="الوثائق والمراسلات الإدارية", color="#0078D4"),
                        Category(name="الوثائق المالية", description="الفواتير والمستندات المالية", color="#107C10"),
                        Category(name="الوثائق القانونية", description="العقود والوثائق القانونية", color="#D13438"),
                        Category(name="الموارد البشرية", description="وثائق الموظفين والموارد البشرية", color="#FF8C00"),
                        Category(name="التقارير", description="التقارير الدورية والإحصائيات", color="#5C2D91"),
                        Category(name="الصور والوسائط", description="الصور والملفات الوسائطية", color="#E3008C"),
                    ]
                    
                    for category in categories:
                        session.add(category)
                    
                    session.commit()
                    self.logger.info("تم إنشاء الفئات الأولية")
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء البيانات الأولية: {e}")
    
    def show_main_window(self):
        """Show the main application window"""
        try:
            # Import here to avoid circular imports
            from src.ui.advanced_main_window import AdvancedMainWindow
            
            # Create main window with all services
            self.main_window = AdvancedMainWindow(
                config=self.config,
                db_manager=self.db_manager,
                services=self.services
            )
            
            # Show login dialog
            from src.ui.login_dialog import LoginDialog
            user = LoginDialog.get_user_login(self.db_manager)
            
            if not user:
                self.logger.info("تم إلغاء تسجيل الدخول")
                return False
            
            # Set current user
            self.main_window.current_user = user
            self.main_window.update_user_info()
            
            # Show main window
            self.main_window.show()
            
            self.logger.info(f"تم عرض النافذة الرئيسية للمستخدم: {user.username}")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في عرض النافذة الرئيسية: {e}")
            QMessageBox.critical(None, "خطأ", f"فشل في عرض النافذة الرئيسية: {e}")
            return False
    
    def run(self) -> int:
        """
        Run the application
        
        Returns:
            Exit code
        """
        try:
            if not self.initialize():
                return 1
            
            if not self.show_main_window():
                return 0  # User cancelled login
            
            # Setup auto-cleanup timer
            cleanup_timer = QTimer()
            cleanup_timer.timeout.connect(self._auto_cleanup)
            cleanup_timer.start(3600000)  # Run every hour
            
            # Run application event loop
            return self.app.exec()
            
        except Exception as e:
            self.logger.error(f"خطأ في تشغيل التطبيق: {e}")
            return 1
    
    def _auto_cleanup(self):
        """Perform automatic cleanup tasks"""
        try:
            # Auto-cleanup trash
            if 'trash' in self.services:
                deleted_count = self.services['trash'].auto_cleanup_trash()
                if deleted_count > 0:
                    self.logger.info(f"تم تنظيف {deleted_count} وثيقة من سلة المهملات تلقائياً")
            
            # Cleanup scanning temp files
            if 'scanning' in self.services:
                self.services['scanning'].cleanup_temp_files()
            
            # Optimize FTS5 index
            if 'search' in self.services:
                self.services['search'].optimize_fts_index()
            
        except Exception as e:
            self.logger.error(f"خطأ في التنظيف التلقائي: {e}")
    
    def get_feature_status(self) -> dict:
        """Get status of all advanced features"""
        return {
            'theme_support': True,
            'high_dpi_support': True,
            'fts5_search': 'search' in self.services,
            'preview_support': 'preview' in self.services,
            'scanning_support': 'scanning' in self.services and self.services['scanning'].wia_available,
            'export_support': 'export' in self.services,
            'trash_support': 'trash' in self.services,
            'database_type': 'SQLite',
            'services_count': len(self.services)
        }


def main():
    """Main entry point"""
    print("=" * 60)
    print("نظام الأرشفة الإلكترونية المتقدم")
    print("Advanced Electronic Archive System")
    print("=" * 60)
    print("الميزات المتقدمة:")
    print("Advanced Features:")
    print("• Modern UI with light/dark themes")
    print("• Full-text search with SQLite FTS5")
    print("• Document thumbnails & previews")
    print("• Scanning support (Windows WIA)")
    print("• Export & print functionality")
    print("• Soft/hard delete with trash")
    print("• Audit trail & history")
    print("=" * 60)
    
    # Create and run application
    app = AdvancedArchiveApp()
    
    try:
        exit_code = app.run()
        print(f"\nتم إنهاء التطبيق بالكود: {exit_code}")
        return exit_code
        
    except KeyboardInterrupt:
        print("\nتم إيقاف التطبيق بواسطة المستخدم")
        return 0
    except Exception as e:
        print(f"\nخطأ غير متوقع: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
