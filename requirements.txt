# Core Dependencies - المتطلبات الأساسية
PySide6>=6.5.0                    # GUI Framework - إطار عمل الواجهة الرسومية
sqlalchemy>=2.0.0                # Database ORM - قاعدة البيانات

# Document Processing & Preview - معالجة الوثائق والمعاينة
PyPDF2>=3.0.1                    # PDF processing - معالجة ملفات PDF
Pillow>=10.0.0                   # Image processing - معالجة الصور
python-docx>=0.8.11              # Word documents - وثائق وورد
openpyxl>=3.1.2                  # Excel files - ملفات إكسل
pdf2image>=1.16.3                # PDF to image conversion - تحويل PDF إلى صور

# Full-text Search - البحث النصي الكامل
whoosh>=2.7.4                    # Search engine - محرك البحث

# Security and Encryption - الأمان والتشفير
bcrypt>=4.0.1                    # Password hashing - تشفير كلمات المرور
cryptography>=41.0.0             # Encryption - التشفير

# Utilities - الأدوات المساعدة
python-dateutil>=2.8.2           # Date utilities - أدوات التاريخ
pathvalidate>=3.2.0              # Path validation - التحقق من المسارات
send2trash>=1.8.2                # Safe file deletion - حذف آمن للملفات

# Development and Testing
pytest>=7.4.0
pytest-qt>=4.2.0

# Windows-specific (Scanning & WIA)
pywin32>=306; sys_platform == "win32"
comtypes>=1.2.0; sys_platform == "win32"

# Packaging
pyinstaller>=6.0.0

# Optional Database Support
pyodbc>=4.0.39
