"""
Scanning Service for Electronic Archive System
خدمة المسح الضوئي لنظام الأرشفة الإلكترونية

Supports WIA (Windows Image Acquisition) with fallback to file import
"""

import logging
import sys
import tempfile
from pathlib import Path
from typing import Optional, List, Dict, Any
from datetime import datetime

from ..core.config import Config


class ScanningService:
    """
    Service for document scanning
    خدمة المسح الضوئي للوثائق
    """
    
    def __init__(self, config: Config):
        """
        Initialize scanning service
        
        Args:
            config: Configuration manager
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Scanning settings
        self.default_dpi = config.getint('SCANNING', 'default_dpi', 300)
        self.default_format = config.get('SCANNING', 'default_format', 'pdf')
        self.auto_crop = config.getboolean('SCANNING', 'auto_crop', True)
        self.auto_rotate = config.getboolean('SCANNING', 'auto_rotate', True)
        
        # Check if WIA is available (Windows only)
        self.wia_available = self._check_wia_availability()
        
        # Temp directory for scanned files
        self.temp_dir = Path(config.storage_paths['temp'])
        self.temp_dir.mkdir(parents=True, exist_ok=True)
    
    def _check_wia_availability(self) -> bool:
        """Check if WIA is available on the system"""
        if sys.platform != "win32":
            return False
        
        try:
            import comtypes.client
            # Try to create WIA DeviceManager
            device_manager = comtypes.client.CreateObject("WIA.DeviceManager")
            return True
        except Exception as e:
            self.logger.warning(f"WIA غير متوفر: {e}")
            return False
    
    def get_available_scanners(self) -> List[Dict[str, Any]]:
        """
        Get list of available scanners
        
        Returns:
            List of scanner information dictionaries
        """
        scanners = []
        
        if not self.wia_available:
            self.logger.warning("WIA غير متوفر، لا يمكن الحصول على قائمة الماسحات الضوئية")
            return scanners
        
        try:
            import comtypes.client
            
            device_manager = comtypes.client.CreateObject("WIA.DeviceManager")
            device_infos = device_manager.DeviceInfos
            
            for i in range(device_infos.Count):
                device_info = device_infos.Item(i + 1)
                
                # Check if device is a scanner
                if device_info.Type == 1:  # Scanner type
                    scanner_info = {
                        'id': device_info.DeviceID,
                        'name': device_info.Properties("Name").Value,
                        'description': device_info.Properties("Description").Value,
                        'manufacturer': device_info.Properties("Manufacturer").Value,
                        'available': True
                    }
                    scanners.append(scanner_info)
            
            self.logger.info(f"تم العثور على {len(scanners)} ماسح ضوئي")
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على قائمة الماسحات الضوئية: {e}")
        
        return scanners
    
    def scan_document(self, scanner_id: Optional[str] = None, 
                     scan_settings: Optional[Dict[str, Any]] = None) -> Optional[str]:
        """
        Scan a document
        
        Args:
            scanner_id: ID of scanner to use (None for default)
            scan_settings: Custom scan settings
            
        Returns:
            Path to scanned file or None if failed
        """
        if not self.wia_available:
            self.logger.error("WIA غير متوفر للمسح الضوئي")
            return None
        
        try:
            import comtypes.client
            
            # Get scanner device
            device = self._get_scanner_device(scanner_id)
            if not device:
                return None
            
            # Configure scan settings
            self._configure_scan_settings(device, scan_settings or {})
            
            # Perform scan
            scanned_item = device.Items(1).Transfer()
            
            # Save scanned image
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            temp_file = self.temp_dir / f"scan_{timestamp}.jpg"
            
            # Save the image
            scanned_item.SaveFile(str(temp_file))
            
            # Post-process if needed
            processed_file = self._post_process_scan(temp_file, scan_settings or {})
            
            self.logger.info(f"تم المسح الضوئي بنجاح: {processed_file}")
            return str(processed_file)
            
        except Exception as e:
            self.logger.error(f"خطأ في المسح الضوئي: {e}")
            return None
    
    def _get_scanner_device(self, scanner_id: Optional[str]):
        """Get scanner device object"""
        try:
            import comtypes.client
            
            device_manager = comtypes.client.CreateObject("WIA.DeviceManager")
            
            if scanner_id:
                # Use specific scanner
                device = device_manager.DeviceInfos(scanner_id).Connect()
            else:
                # Use first available scanner
                device_infos = device_manager.DeviceInfos
                
                for i in range(device_infos.Count):
                    device_info = device_infos.Item(i + 1)
                    if device_info.Type == 1:  # Scanner type
                        device = device_info.Connect()
                        break
                else:
                    self.logger.error("لم يتم العثور على ماسح ضوئي متاح")
                    return None
            
            return device
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على جهاز الماسح الضوئي: {e}")
            return None
    
    def _configure_scan_settings(self, device, settings: Dict[str, Any]):
        """Configure scanner settings"""
        try:
            item = device.Items(1)
            
            # Set DPI
            dpi = settings.get('dpi', self.default_dpi)
            try:
                item.Properties("Horizontal Resolution").Value = dpi
                item.Properties("Vertical Resolution").Value = dpi
            except:
                pass  # Some scanners may not support this property
            
            # Set color mode
            color_mode = settings.get('color_mode', 'color')
            try:
                if color_mode == 'grayscale':
                    item.Properties("Current Intent").Value = 2  # Grayscale
                elif color_mode == 'bw':
                    item.Properties("Current Intent").Value = 4  # Black and white
                else:
                    item.Properties("Current Intent").Value = 1  # Color
            except:
                pass
            
            # Set format
            format_type = settings.get('format', 'jpeg')
            try:
                if format_type.lower() == 'png':
                    item.Properties("Format").Value = "{B96B3CAF-0728-11D3-9D7B-0000F81EF32E}"
                else:
                    item.Properties("Format").Value = "{B96B3CAE-0728-11D3-9D7B-0000F81EF32E}"  # JPEG
            except:
                pass
            
        except Exception as e:
            self.logger.warning(f"تحذير في تكوين إعدادات المسح: {e}")
    
    def _post_process_scan(self, scanned_file: Path, settings: Dict[str, Any]) -> Path:
        """Post-process scanned image"""
        try:
            from PIL import Image, ImageOps
            
            with Image.open(scanned_file) as img:
                # Auto-rotate if enabled
                if self.auto_rotate and settings.get('auto_rotate', True):
                    img = ImageOps.exif_transpose(img)
                
                # Auto-crop if enabled
                if self.auto_crop and settings.get('auto_crop', True):
                    # Simple auto-crop by removing white borders
                    try:
                        # Convert to grayscale for edge detection
                        gray = img.convert('L')
                        # Get bounding box of non-white content
                        bbox = gray.getbbox()
                        if bbox:
                            img = img.crop(bbox)
                    except:
                        pass  # Skip auto-crop if it fails
                
                # Convert to desired format
                output_format = settings.get('format', self.default_format).lower()
                
                if output_format == 'pdf':
                    # Convert to PDF
                    output_file = scanned_file.with_suffix('.pdf')
                    if img.mode != 'RGB':
                        img = img.convert('RGB')
                    img.save(output_file, 'PDF', quality=95)
                elif output_format == 'png':
                    # Save as PNG
                    output_file = scanned_file.with_suffix('.png')
                    img.save(output_file, 'PNG', optimize=True)
                else:
                    # Save as JPEG (default)
                    output_file = scanned_file.with_suffix('.jpg')
                    if img.mode != 'RGB':
                        img = img.convert('RGB')
                    img.save(output_file, 'JPEG', quality=95, optimize=True)
                
                # Remove original if different format
                if output_file != scanned_file:
                    scanned_file.unlink()
                
                return output_file
                
        except Exception as e:
            self.logger.error(f"خطأ في معالجة الصورة الممسوحة: {e}")
            return scanned_file
    
    def scan_multiple_pages(self, scanner_id: Optional[str] = None,
                           scan_settings: Optional[Dict[str, Any]] = None) -> List[str]:
        """
        Scan multiple pages
        
        Args:
            scanner_id: ID of scanner to use
            scan_settings: Custom scan settings
            
        Returns:
            List of paths to scanned files
        """
        scanned_files = []
        
        try:
            # For now, scan one page at a time
            # In the future, could implement automatic document feeder support
            page_num = 1
            
            while True:
                print(f"المسح الضوئي للصفحة {page_num}")
                print("اضغط Enter للمتابعة أو 'q' للإنهاء:")
                
                user_input = input().strip().lower()
                if user_input == 'q':
                    break
                
                scanned_file = self.scan_document(scanner_id, scan_settings)
                if scanned_file:
                    scanned_files.append(scanned_file)
                    page_num += 1
                else:
                    break
            
            self.logger.info(f"تم مسح {len(scanned_files)} صفحة")
            
        except Exception as e:
            self.logger.error(f"خطأ في المسح متعدد الصفحات: {e}")
        
        return scanned_files
    
    def combine_pages_to_pdf(self, image_files: List[str], output_path: str) -> bool:
        """
        Combine multiple scanned pages into a single PDF
        
        Args:
            image_files: List of image file paths
            output_path: Output PDF path
            
        Returns:
            True if successful
        """
        try:
            from PIL import Image
            
            if not image_files:
                return False
            
            # Open all images
            images = []
            for file_path in image_files:
                img = Image.open(file_path)
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                images.append(img)
            
            # Save as PDF
            if images:
                images[0].save(
                    output_path,
                    'PDF',
                    save_all=True,
                    append_images=images[1:],
                    quality=95
                )
                
                self.logger.info(f"تم دمج {len(images)} صفحة في PDF: {output_path}")
                return True
            
        except Exception as e:
            self.logger.error(f"خطأ في دمج الصفحات: {e}")
        
        return False
    
    def cleanup_temp_files(self, max_age_hours: int = 24):
        """
        Clean up old temporary scan files
        
        Args:
            max_age_hours: Maximum age of files to keep in hours
        """
        try:
            import time
            
            current_time = time.time()
            max_age_seconds = max_age_hours * 3600
            
            cleaned_count = 0
            for file_path in self.temp_dir.glob("scan_*"):
                if file_path.is_file():
                    file_age = current_time - file_path.stat().st_mtime
                    if file_age > max_age_seconds:
                        file_path.unlink()
                        cleaned_count += 1
            
            if cleaned_count > 0:
                self.logger.info(f"تم تنظيف {cleaned_count} ملف مؤقت")
                
        except Exception as e:
            self.logger.error(f"خطأ في تنظيف الملفات المؤقتة: {e}")
    
    def get_scan_settings_template(self) -> Dict[str, Any]:
        """Get template for scan settings"""
        return {
            'dpi': self.default_dpi,
            'color_mode': 'color',  # 'color', 'grayscale', 'bw'
            'format': self.default_format,  # 'jpeg', 'png', 'pdf'
            'auto_crop': self.auto_crop,
            'auto_rotate': self.auto_rotate,
            'quality': 95
        }
