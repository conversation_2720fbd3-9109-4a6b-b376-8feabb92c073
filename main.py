#!/usr/bin/env python3
"""
Electronic Archive System - Main Entry Point
نظام الأرشفة الإلكترونية - نقطة الدخول الرئيسية

A production-ready Electronic Archiving System built with Python 3.11+ and PySide6 (Qt 6)
with SQL Server, designed to run primarily on Windows 10+.
"""

import sys
import os
import logging
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import QTranslator, QLocale
from PySide6.QtGui import QIcon

from src.core.config import Config
from src.core.database import DatabaseManager
from src.ui.main_window import MainWindow
from src.ui.login_dialog import LoginDialog
from src.core.logger import setup_logging


def setup_application():
    """إعداد التطبيق الأساسي"""
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("Electronic Archive System")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("Archive Solutions")
    
    # Set application icon
    icon_path = project_root / "resources" / "icons" / "app_icon.ico"
    if icon_path.exists():
        app.setWindowIcon(QIcon(str(icon_path)))
    
    return app


def setup_translations(app):
    """إعداد الترجمة والتوطين"""
    translator = QTranslator()
    locale = QLocale.system().name()
    
    # Load Arabic translation if available
    translation_path = project_root / "resources" / "translations" / f"archive_{locale}.qm"
    if translation_path.exists():
        translator.load(str(translation_path))
        app.installTranslator(translator)
    
    return translator


def check_dependencies():
    """فحص التبعيات المطلوبة"""
    try:
        import pyodbc
        import sqlalchemy
        from PySide6 import QtWidgets
        return True
    except ImportError as e:
        QMessageBox.critical(
            None,
            "خطأ في التبعيات",
            f"مكتبة مطلوبة غير موجودة: {e}\n"
            "يرجى تثبيت المتطلبات باستخدام: pip install -r requirements.txt"
        )
        return False


def main():
    """الدالة الرئيسية للتطبيق"""
    try:
        # Setup logging
        setup_logging()
        logger = logging.getLogger(__name__)
        logger.info("بدء تشغيل نظام الأرشفة الإلكترونية")
        
        # Check dependencies
        if not check_dependencies():
            return 1
        
        # Setup application
        app = setup_application()
        
        # Setup translations
        translator = setup_translations(app)
        
        # Load configuration
        config = Config()
        
        # Initialize database
        db_manager = DatabaseManager(config)
        if not db_manager.initialize():
            QMessageBox.critical(
                None,
                "خطأ في قاعدة البيانات",
                "فشل في الاتصال بقاعدة البيانات. يرجى التحقق من الإعدادات."
            )
            return 1

        # Show login dialog
        user = LoginDialog.get_user_login(db_manager)
        if not user:
            # User cancelled login
            return 0

        # Create and show main window
        main_window = MainWindow(config, db_manager)
        main_window.current_user = user
        main_window.user_label.setText(f"مرحباً، {user.full_name or user.username}")
        main_window.show()
        
        logger.info("تم تشغيل النظام بنجاح")
        
        # Run application
        return app.exec()
        
    except Exception as e:
        logger.error(f"خطأ في تشغيل التطبيق: {e}")
        QMessageBox.critical(
            None,
            "خطأ في النظام",
            f"حدث خطأ غير متوقع: {e}"
        )
        return 1


if __name__ == "__main__":
    sys.exit(main())
