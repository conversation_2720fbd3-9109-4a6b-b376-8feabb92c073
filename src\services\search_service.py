"""
Search Service for Electronic Archive System
خدمة البحث لنظام الأرشفة الإلكترونية

Provides full-text search capabilities using SQL Server Full-Text Search
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, date
from sqlalchemy import text, and_, or_
from sqlalchemy.orm import Session

from ..models import Document, Category, User
from ..core.database import DatabaseManager


class SearchService:
    """
    Service for document search and indexing
    خدمة البحث والفهرسة للوثائق
    """
    
    def __init__(self, db_manager: DatabaseManager):
        """
        Initialize search service
        
        Args:
            db_manager: Database manager instance
        """
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)
    
    def search_documents(self, query: str, filters: Optional[Dict[str, Any]] = None,
                        limit: int = 100, offset: int = 0) -> Tuple[List[Document], int]:
        """
        Search documents with full-text search and filters
        
        Args:
            query: Search query string
            filters: Additional filters (category_id, file_type, date_range, etc.)
            limit: Maximum number of results
            offset: Offset for pagination
            
        Returns:
            Tuple of (documents list, total count)
        """
        try:
            with self.db_manager.get_session() as session:
                # Build base query
                base_query = session.query(Document).filter(Document.is_active == True)
                
                # Apply full-text search if query provided
                if query and query.strip():
                    # Use SQL Server Full-Text Search
                    search_condition = self._build_fulltext_search(query.strip())
                    base_query = base_query.filter(text(search_condition))
                
                # Apply filters
                if filters:
                    base_query = self._apply_filters(base_query, filters)
                
                # Get total count
                total_count = base_query.count()
                
                # Apply pagination and ordering
                documents = (base_query
                           .order_by(Document.created_at.desc())
                           .offset(offset)
                           .limit(limit)
                           .all())
                
                return documents, total_count
                
        except Exception as e:
            self.logger.error(f"خطأ في البحث: {e}")
            return [], 0
    
    def _build_fulltext_search(self, query: str) -> str:
        """
        Build SQL Server full-text search condition
        
        Args:
            query: Search query
            
        Returns:
            SQL condition string
        """
        # Clean and prepare query for full-text search
        cleaned_query = self._clean_search_query(query)
        
        # Build CONTAINS condition for full-text search
        search_condition = f"""
        (CONTAINS((title, description, full_text_content), '{cleaned_query}')
         OR title LIKE '%{query}%'
         OR description LIKE '%{query}%'
         OR tags LIKE '%{query}%')
        """
        
        return search_condition
    
    def _clean_search_query(self, query: str) -> str:
        """
        Clean and prepare query for SQL Server full-text search
        
        Args:
            query: Raw search query
            
        Returns:
            Cleaned query string
        """
        # Remove special characters that might cause issues
        special_chars = ['[', ']', '(', ')', '{', '}', '"', "'", '\\', '/', '*', '?']
        cleaned = query
        
        for char in special_chars:
            cleaned = cleaned.replace(char, ' ')
        
        # Split into words and join with AND
        words = [word.strip() for word in cleaned.split() if word.strip()]
        
        if len(words) == 1:
            return f'"{words[0]}"'
        elif len(words) > 1:
            # Use AND for multiple words
            return ' AND '.join([f'"{word}"' for word in words])
        else:
            return '""'
    
    def _apply_filters(self, query, filters: Dict[str, Any]):
        """
        Apply search filters to query
        
        Args:
            query: SQLAlchemy query object
            filters: Dictionary of filters
            
        Returns:
            Modified query object
        """
        # Category filter
        if 'category_id' in filters and filters['category_id']:
            if filters.get('include_subcategories', False):
                # Include subcategories
                category_ids = self._get_category_and_children_ids(filters['category_id'])
                query = query.filter(Document.category_id.in_(category_ids))
            else:
                query = query.filter(Document.category_id == filters['category_id'])
        
        # File type filter
        if 'file_type' in filters and filters['file_type']:
            query = query.filter(Document.file_type == filters['file_type'])
        
        # Date range filter
        if 'date_from' in filters and filters['date_from']:
            query = query.filter(Document.document_date >= filters['date_from'])
        
        if 'date_to' in filters and filters['date_to']:
            query = query.filter(Document.document_date <= filters['date_to'])
        
        # Created by filter
        if 'created_by' in filters and filters['created_by']:
            query = query.filter(Document.created_by == filters['created_by'])
        
        # File size filter
        if 'min_size' in filters and filters['min_size']:
            query = query.filter(Document.file_size >= filters['min_size'])
        
        if 'max_size' in filters and filters['max_size']:
            query = query.filter(Document.file_size <= filters['max_size'])
        
        # Tags filter
        if 'tags' in filters and filters['tags']:
            tag_conditions = []
            for tag in filters['tags']:
                tag_conditions.append(Document.tags.contains(tag))
            query = query.filter(or_(*tag_conditions))
        
        return query
    
    def _get_category_and_children_ids(self, category_id: int) -> List[int]:
        """
        Get category ID and all its children IDs
        
        Args:
            category_id: Parent category ID
            
        Returns:
            List of category IDs
        """
        try:
            with self.db_manager.get_session() as session:
                category = session.query(Category).get(category_id)
                if not category:
                    return [category_id]
                
                # Get all children recursively
                all_children = category.get_all_children()
                category_ids = [category.id] + [child.id for child in all_children]
                
                return category_ids
                
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على فئات فرعية: {e}")
            return [category_id]
    
    def search_suggestions(self, partial_query: str, limit: int = 10) -> List[str]:
        """
        Get search suggestions based on partial query
        
        Args:
            partial_query: Partial search query
            limit: Maximum number of suggestions
            
        Returns:
            List of search suggestions
        """
        try:
            with self.db_manager.get_session() as session:
                suggestions = []
                
                # Search in document titles
                title_matches = (session.query(Document.title)
                               .filter(Document.title.contains(partial_query))
                               .filter(Document.is_active == True)
                               .distinct()
                               .limit(limit // 2)
                               .all())
                
                suggestions.extend([match[0] for match in title_matches])
                
                # Search in tags
                tag_matches = (session.query(Document.tags)
                             .filter(Document.tags.contains(partial_query))
                             .filter(Document.is_active == True)
                             .distinct()
                             .limit(limit // 2)
                             .all())
                
                # Extract individual tags
                for tag_string in tag_matches:
                    if tag_string[0]:
                        tags = [tag.strip() for tag in tag_string[0].split(',')]
                        matching_tags = [tag for tag in tags if partial_query.lower() in tag.lower()]
                        suggestions.extend(matching_tags)
                
                # Remove duplicates and limit
                unique_suggestions = list(set(suggestions))[:limit]
                
                return unique_suggestions
                
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على اقتراحات البحث: {e}")
            return []
    
    def get_popular_searches(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get popular search terms (this would require search logging)
        
        Args:
            limit: Maximum number of results
            
        Returns:
            List of popular search terms with counts
        """
        # This would be implemented with search logging
        # For now, return empty list
        return []
    
    def advanced_search(self, criteria: Dict[str, Any]) -> Tuple[List[Document], int]:
        """
        Perform advanced search with multiple criteria
        
        Args:
            criteria: Advanced search criteria
            
        Returns:
            Tuple of (documents list, total count)
        """
        try:
            with self.db_manager.get_session() as session:
                query = session.query(Document).filter(Document.is_active == True)
                
                # Title search
                if criteria.get('title'):
                    query = query.filter(Document.title.contains(criteria['title']))
                
                # Description search
                if criteria.get('description'):
                    query = query.filter(Document.description.contains(criteria['description']))
                
                # Content search
                if criteria.get('content'):
                    query = query.filter(Document.full_text_content.contains(criteria['content']))
                
                # Exact file name
                if criteria.get('file_name'):
                    query = query.filter(Document.file_name.contains(criteria['file_name']))
                
                # Multiple categories
                if criteria.get('categories'):
                    query = query.filter(Document.category_id.in_(criteria['categories']))
                
                # Multiple file types
                if criteria.get('file_types'):
                    query = query.filter(Document.file_type.in_(criteria['file_types']))
                
                # Date range
                if criteria.get('date_from'):
                    query = query.filter(Document.document_date >= criteria['date_from'])
                
                if criteria.get('date_to'):
                    query = query.filter(Document.document_date <= criteria['date_to'])
                
                # Size range
                if criteria.get('size_from'):
                    query = query.filter(Document.file_size >= criteria['size_from'])
                
                if criteria.get('size_to'):
                    query = query.filter(Document.file_size <= criteria['size_to'])
                
                # Multiple creators
                if criteria.get('creators'):
                    query = query.filter(Document.created_by.in_(criteria['creators']))
                
                # Get total count
                total_count = query.count()
                
                # Apply ordering and pagination
                limit = criteria.get('limit', 100)
                offset = criteria.get('offset', 0)
                
                documents = (query
                           .order_by(Document.created_at.desc())
                           .offset(offset)
                           .limit(limit)
                           .all())
                
                return documents, total_count
                
        except Exception as e:
            self.logger.error(f"خطأ في البحث المتقدم: {e}")
            return [], 0
    
    def rebuild_search_index(self) -> bool:
        """
        Rebuild full-text search index
        
        Returns:
            True if successful
        """
        try:
            with self.db_manager.get_session() as session:
                # Rebuild full-text catalog
                session.execute(text("ALTER FULLTEXT CATALOG ArchiveCatalog REBUILD"))
                session.commit()
                
                self.logger.info("تم إعادة بناء فهرس البحث بنجاح")
                return True
                
        except Exception as e:
            self.logger.error(f"خطأ في إعادة بناء فهرس البحث: {e}")
            return False
