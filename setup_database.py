#!/usr/bin/env python3
"""
Database Setup Script for Electronic Archive System
سكريبت إعداد قاعدة البيانات لنظام الأرشفة الإلكترونية

Sets up the database schema and creates initial data
"""

import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.config import Config
from src.core.database import DatabaseManager
from src.core.logger import setup_logging
from src.models import User, Category, UserRole


def create_initial_categories(db_manager: DatabaseManager):
    """إنشاء الفئات الأولية"""
    print("إنشاء الفئات الأولية...")
    
    try:
        with db_manager.get_session() as session:
            # Check if categories already exist
            existing_categories = session.query(Category).count()
            if existing_categories > 0:
                print("الفئات موجودة بالفعل، تخطي إنشاء الفئات الأولية")
                return
            
            # Create root categories
            categories_data = [
                {
                    'name': 'الوثائق الإدارية',
                    'description': 'الوثائق والمراسلات الإدارية',
                    'color': '#0078D4'
                },
                {
                    'name': 'الوثائق المالية',
                    'description': 'الفواتير والمستندات المالية',
                    'color': '#107C10'
                },
                {
                    'name': 'الوثائق القانونية',
                    'description': 'العقود والوثائق القانونية',
                    'color': '#D13438'
                },
                {
                    'name': 'الموارد البشرية',
                    'description': 'وثائق الموظفين والموارد البشرية',
                    'color': '#FF8C00'
                },
                {
                    'name': 'التقارير',
                    'description': 'التقارير الدورية والإحصائيات',
                    'color': '#5C2D91'
                },
                {
                    'name': 'الصور والوسائط',
                    'description': 'الصور والملفات الوسائطية',
                    'color': '#E3008C'
                }
            ]
            
            created_categories = []
            for cat_data in categories_data:
                category = Category(**cat_data)
                session.add(category)
                created_categories.append(category)
            
            session.commit()
            
            # Create subcategories
            subcategories_data = [
                # Administrative subcategories
                {
                    'name': 'المراسلات الداخلية',
                    'description': 'المراسلات بين الأقسام',
                    'parent_name': 'الوثائق الإدارية'
                },
                {
                    'name': 'المراسلات الخارجية',
                    'description': 'المراسلات مع الجهات الخارجية',
                    'parent_name': 'الوثائق الإدارية'
                },
                
                # Financial subcategories
                {
                    'name': 'الفواتير',
                    'description': 'فواتير المشتريات والمبيعات',
                    'parent_name': 'الوثائق المالية'
                },
                {
                    'name': 'الإيصالات',
                    'description': 'إيصالات الدفع والاستلام',
                    'parent_name': 'الوثائق المالية'
                },
                
                # Legal subcategories
                {
                    'name': 'العقود',
                    'description': 'عقود العمل والخدمات',
                    'parent_name': 'الوثائق القانونية'
                },
                {
                    'name': 'التراخيص',
                    'description': 'التراخيص والتصاريح',
                    'parent_name': 'الوثائق القانونية'
                }
            ]
            
            for subcat_data in subcategories_data:
                parent_name = subcat_data.pop('parent_name')
                parent = next((cat for cat in created_categories if cat.name == parent_name), None)
                
                if parent:
                    subcat_data['parent_id'] = parent.id
                    subcategory = Category(**subcat_data)
                    session.add(subcategory)
            
            session.commit()
            print(f"تم إنشاء {len(categories_data)} فئة رئيسية و {len(subcategories_data)} فئة فرعية")
            
    except Exception as e:
        print(f"خطأ في إنشاء الفئات الأولية: {e}")


def create_admin_user(db_manager: DatabaseManager):
    """إنشاء مستخدم المدير الأول"""
    print("إنشاء مستخدم المدير...")
    
    try:
        with db_manager.get_session() as session:
            # Check if admin user already exists
            existing_admin = session.query(User).filter(User.role == UserRole.ADMIN).first()
            if existing_admin:
                print("مستخدم المدير موجود بالفعل، تخطي إنشاء المدير")
                return
            
            # Create admin user
            admin_user = User.create_admin_user(
                session=session,
                username="admin",
                email="<EMAIL>",
                password="Admin123!",
                full_name="مدير النظام"
            )
            
            print(f"تم إنشاء مستخدم المدير: {admin_user.username}")
            print("بيانات تسجيل الدخول:")
            print("اسم المستخدم: admin")
            print("كلمة المرور: Admin123!")
            print("يرجى تغيير كلمة المرور بعد أول تسجيل دخول")
            
    except Exception as e:
        print(f"خطأ في إنشاء مستخدم المدير: {e}")


def setup_database():
    """إعداد قاعدة البيانات الكاملة"""
    print("بدء إعداد قاعدة البيانات...")
    
    try:
        # Setup logging
        setup_logging()
        
        # Load configuration
        config = Config()
        
        # Initialize database
        db_manager = DatabaseManager(config)
        
        print("الاتصال بقاعدة البيانات...")
        if not db_manager.initialize():
            print("فشل في الاتصال بقاعدة البيانات")
            return False
        
        print("تم الاتصال بقاعدة البيانات بنجاح")
        
        # Test connection
        if not db_manager.test_connection():
            print("فشل في اختبار الاتصال بقاعدة البيانات")
            return False
        
        print("تم اختبار الاتصال بنجاح")
        
        # Create initial data
        create_initial_categories(db_manager)
        create_admin_user(db_manager)
        
        print("تم إعداد قاعدة البيانات بنجاح!")
        print("\nيمكنك الآن تشغيل التطبيق باستخدام:")
        print("python main.py")
        
        return True
        
    except Exception as e:
        print(f"خطأ في إعداد قاعدة البيانات: {e}")
        return False


def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("إعداد قاعدة البيانات - نظام الأرشفة الإلكترونية")
    print("Database Setup - Electronic Archive System")
    print("=" * 60)
    
    # Check if config file exists
    config_file = Path("config.ini")
    if not config_file.exists():
        print("ملف التكوين غير موجود. سيتم إنشاء ملف تكوين افتراضي.")
        print("يرجى تحديث إعدادات قاعدة البيانات في config.ini قبل المتابعة.")
        
        # Create default config
        config = Config()
        config.save_config()
        
        print("تم إنشاء ملف config.ini")
        print("يرجى تحديث إعدادات قاعدة البيانات ثم تشغيل هذا السكريبت مرة أخرى.")
        return
    
    # Confirm setup
    response = input("\nهل تريد المتابعة مع إعداد قاعدة البيانات؟ (y/n): ")
    if response.lower() not in ['y', 'yes', 'نعم']:
        print("تم إلغاء الإعداد")
        return
    
    # Run setup
    success = setup_database()
    
    if success:
        print("\n" + "=" * 60)
        print("تم إعداد قاعدة البيانات بنجاح!")
        print("Database setup completed successfully!")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("فشل في إعداد قاعدة البيانات")
        print("Database setup failed")
        print("=" * 60)
        sys.exit(1)


if __name__ == "__main__":
    main()
