"""
Category Model for Electronic Archive System
نموذج الفئة لنظام الأرشفة الإلكترونية

Handles document categorization and hierarchical organization
"""

from typing import Optional, List
from sqlalchemy import Column, String, Integer, ForeignKey
from sqlalchemy.orm import relationship, Session

from .base import BaseModel


class Category(BaseModel):
    """
    Category model for organizing documents
    نموذج الفئة لتنظيم الوثائق
    """
    __tablename__ = "categories"
    
    name = Column(String(100), nullable=False, index=True)
    description = Column(String(500), nullable=True)
    parent_id = Column(Integer, ForeignKey('categories.id'), nullable=True)
    color = Column(String(7), default='#0078D4', nullable=False)  # Hex color code
    
    # Self-referential relationship for hierarchical categories
    parent = relationship("Category", remote_side="Category.id", back_populates="children")
    children = relationship("Category", back_populates="parent", lazy="dynamic")
    
    # Relationship with documents
    documents = relationship("Document", back_populates="category", lazy="dynamic")
    
    @property
    def full_path(self) -> str:
        """
        Get full category path (e.g., "Parent > Child > Grandchild")
        
        Returns:
            Full category path
        """
        if self.parent:
            return f"{self.parent.full_path} > {self.name}"
        return self.name
    
    @property
    def level(self) -> int:
        """
        Get category level in hierarchy (0 for root categories)
        
        Returns:
            Category level
        """
        if self.parent:
            return self.parent.level + 1
        return 0
    
    @property
    def document_count(self) -> int:
        """
        Get number of documents in this category (including subcategories)
        
        Returns:
            Document count
        """
        count = self.documents.count()
        
        # Add documents from subcategories
        for child in self.children:
            count += child.document_count
        
        return count
    
    def get_all_children(self) -> List['Category']:
        """
        Get all descendant categories (recursive)
        
        Returns:
            List of all descendant categories
        """
        all_children = []
        
        for child in self.children:
            all_children.append(child)
            all_children.extend(child.get_all_children())
        
        return all_children
    
    def get_ancestors(self) -> List['Category']:
        """
        Get all ancestor categories
        
        Returns:
            List of ancestor categories (from root to parent)
        """
        ancestors = []
        current = self.parent
        
        while current:
            ancestors.insert(0, current)
            current = current.parent
        
        return ancestors
    
    def can_be_parent_of(self, potential_child: 'Category') -> bool:
        """
        Check if this category can be parent of another category
        (prevents circular references)
        
        Args:
            potential_child: Category to check
            
        Returns:
            True if can be parent
        """
        if potential_child.id == self.id:
            return False
        
        # Check if potential_child is an ancestor of self
        ancestors = self.get_ancestors()
        return potential_child not in ancestors
    
    @classmethod
    def get_root_categories(cls, session: Session) -> List['Category']:
        """
        Get all root categories (categories without parent)
        
        Args:
            session: Database session
            
        Returns:
            List of root categories
        """
        return session.query(cls).filter(
            cls.parent_id.is_(None),
            cls.is_active == True
        ).order_by(cls.name).all()
    
    @classmethod
    def get_by_name(cls, session: Session, name: str, parent_id: Optional[int] = None) -> Optional['Category']:
        """
        Get category by name and optional parent
        
        Args:
            session: Database session
            name: Category name
            parent_id: Parent category ID
            
        Returns:
            Category instance or None
        """
        query = session.query(cls).filter(
            cls.name == name,
            cls.is_active == True
        )
        
        if parent_id is not None:
            query = query.filter(cls.parent_id == parent_id)
        else:
            query = query.filter(cls.parent_id.is_(None))
        
        return query.first()
    
    @classmethod
    def create_hierarchy(cls, session: Session, path: str, separator: str = " > ") -> 'Category':
        """
        Create category hierarchy from path string
        
        Args:
            session: Database session
            path: Category path (e.g., "Documents > Legal > Contracts")
            separator: Path separator
            
        Returns:
            Leaf category
        """
        parts = [part.strip() for part in path.split(separator)]
        current_parent = None
        
        for part in parts:
            # Check if category exists at this level
            existing = cls.get_by_name(
                session, 
                part, 
                current_parent.id if current_parent else None
            )
            
            if existing:
                current_parent = existing
            else:
                # Create new category
                new_category = cls(
                    name=part,
                    parent_id=current_parent.id if current_parent else None
                )
                new_category.save(session)
                current_parent = new_category
        
        return current_parent
    
    def move_to_parent(self, session: Session, new_parent: Optional['Category']):
        """
        Move category to new parent
        
        Args:
            session: Database session
            new_parent: New parent category (None for root)
        """
        if new_parent and not new_parent.can_be_parent_of(self):
            raise ValueError("لا يمكن نقل الفئة إلى هذا الموقع (مرجع دائري)")
        
        self.parent_id = new_parent.id if new_parent else None
        self.save(session)
    
    def delete(self, session: Session, soft_delete: bool = True, move_children_to_parent: bool = True):
        """
        Delete category with options for handling children
        
        Args:
            session: Database session
            soft_delete: If True, mark as inactive instead of deleting
            move_children_to_parent: If True, move children to this category's parent
        """
        if move_children_to_parent:
            # Move children to this category's parent
            for child in self.children:
                child.parent_id = self.parent_id
                child.save(session)
        
        # Call parent delete method
        super().delete(session, soft_delete)
    
    def to_dict(self, include_children: bool = False, include_documents: bool = False) -> dict:
        """
        Convert category to dictionary
        
        Args:
            include_children: Include child categories
            include_documents: Include document count
            
        Returns:
            Dictionary representation
        """
        data = super().to_dict()
        data['full_path'] = self.full_path
        data['level'] = self.level
        
        if include_documents:
            data['document_count'] = self.document_count
        
        if include_children:
            data['children'] = [
                child.to_dict(include_children=True, include_documents=include_documents)
                for child in self.children.filter_by(is_active=True).all()
            ]
        
        return data
    
    def __repr__(self):
        return f"<Category(id={self.id}, name='{self.name}', parent_id={self.parent_id})>"
