"""
Utilities module for Electronic Archive System
وحدة الأدوات المساعدة لنظام الأرشفة الإلكترونية

Contains utility functions and helper classes for common operations
including file handling, encryption, validation, and formatting.
"""

from .helpers import (
    format_file_size,
    format_date_arabic,
    sanitize_filename,
    validate_email,
    generate_unique_filename,
    calculate_md5,
    get_mime_type,
    is_image_file,
    is_document_file,
    clean_text,
    parse_tags,
    format_tags,
    truncate_text,
    safe_int,
    safe_float,
    dict_to_query_string,
    ensure_directory,
    get_file_icon_name,
    validate_password_strength
)

# Export all utilities
__all__ = [
    'format_file_size',
    'format_date_arabic',
    'sanitize_filename',
    'validate_email',
    'generate_unique_filename',
    'calculate_md5',
    'get_mime_type',
    'is_image_file',
    'is_document_file',
    'clean_text',
    'parse_tags',
    'format_tags',
    'truncate_text',
    'safe_int',
    'safe_float',
    'dict_to_query_string',
    'ensure_directory',
    'get_file_icon_name',
    'validate_password_strength'
]
