"""
Trash Service for Electronic Archive System
خدمة سلة المهملات لنظام الأرشفة الإلكترونية

Handles soft/hard delete functionality with recovery options
"""

import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from pathlib import Path

try:
    from send2trash import send2trash
    SEND2TRASH_AVAILABLE = True
except ImportError:
    SEND2TRASH_AVAILABLE = False

from sqlalchemy.orm import Session
from sqlalchemy import text

from ..core.database_sqlite import DatabaseManagerSQLite
from ..models import Document, AuditLog


class TrashService:
    """
    Service for handling document deletion and recovery
    خدمة التعامل مع حذف واستعادة الوثائق
    """
    
    def __init__(self, db_manager: DatabaseManagerSQLite):
        """
        Initialize trash service
        
        Args:
            db_manager: Database manager
        """
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)
        
        # Auto-cleanup settings
        self.auto_cleanup_days = 30  # Days to keep in trash before permanent deletion
    
    def soft_delete_document(self, document_id: int, deleted_by: int, 
                           reason: Optional[str] = None) -> bool:
        """
        Soft delete a document (move to trash)
        
        Args:
            document_id: Document ID to delete
            deleted_by: User ID who deleted the document
            reason: Optional reason for deletion
            
        Returns:
            True if successful
        """
        try:
            with self.db_manager.get_session() as session:
                # Get document
                document = session.query(Document).filter(
                    Document.id == document_id,
                    Document.is_active == True
                ).first()
                
                if not document:
                    self.logger.warning(f"الوثيقة غير موجودة أو محذوفة بالفعل: {document_id}")
                    return False
                
                # Store document info for audit
                document_info = document.to_dict()
                
                # Mark as deleted (soft delete)
                document.is_active = False
                document.updated_at = datetime.utcnow()
                
                # Add deletion metadata
                deletion_info = {
                    'deleted_at': datetime.utcnow().isoformat(),
                    'deleted_by': deleted_by,
                    'deletion_reason': reason,
                    'deletion_type': 'soft'
                }
                
                # Create trash record
                self._create_trash_record(session, document, deletion_info)
                
                session.commit()
                
                # Log audit trail
                AuditLog.log_action(
                    session, deleted_by, "document_soft_delete", "documents", document_id,
                    document_info, deletion_info
                )
                
                self.logger.info(f"تم الحذف الناعم للوثيقة: {document.title} (ID: {document_id})")
                return True
                
        except Exception as e:
            self.logger.error(f"خطأ في الحذف الناعم: {e}")
            return False
    
    def hard_delete_document(self, document_id: int, deleted_by: int,
                           reason: Optional[str] = None) -> bool:
        """
        Hard delete a document (permanent deletion)
        
        Args:
            document_id: Document ID to delete
            deleted_by: User ID who deleted the document
            reason: Optional reason for deletion
            
        Returns:
            True if successful
        """
        try:
            with self.db_manager.get_session() as session:
                # Get document
                document = session.query(Document).filter(
                    Document.id == document_id
                ).first()
                
                if not document:
                    self.logger.warning(f"الوثيقة غير موجودة: {document_id}")
                    return False
                
                # Store document info for audit
                document_info = document.to_dict()
                
                # Delete physical files
                self._delete_physical_files(document)
                
                # Delete from database
                session.delete(document)
                
                # Remove from trash if exists
                self._remove_trash_record(session, document_id)
                
                session.commit()
                
                # Log audit trail
                deletion_info = {
                    'deleted_at': datetime.utcnow().isoformat(),
                    'deleted_by': deleted_by,
                    'deletion_reason': reason,
                    'deletion_type': 'hard'
                }
                
                AuditLog.log_action(
                    session, deleted_by, "document_hard_delete", "documents", document_id,
                    document_info, deletion_info
                )
                
                self.logger.info(f"تم الحذف النهائي للوثيقة: {document_info['title']} (ID: {document_id})")
                return True
                
        except Exception as e:
            self.logger.error(f"خطأ في الحذف النهائي: {e}")
            return False
    
    def restore_document(self, document_id: int, restored_by: int) -> bool:
        """
        Restore a soft-deleted document
        
        Args:
            document_id: Document ID to restore
            restored_by: User ID who restored the document
            
        Returns:
            True if successful
        """
        try:
            with self.db_manager.get_session() as session:
                # Get deleted document
                document = session.query(Document).filter(
                    Document.id == document_id,
                    Document.is_active == False
                ).first()
                
                if not document:
                    self.logger.warning(f"الوثيقة المحذوفة غير موجودة: {document_id}")
                    return False
                
                # Check if physical file still exists
                if not Path(document.file_path).exists():
                    self.logger.error(f"الملف الفيزيائي غير موجود، لا يمكن الاستعادة: {document.file_path}")
                    return False
                
                # Restore document
                document.is_active = True
                document.updated_at = datetime.utcnow()
                
                # Remove from trash
                self._remove_trash_record(session, document_id)
                
                session.commit()
                
                # Log audit trail
                restore_info = {
                    'restored_at': datetime.utcnow().isoformat(),
                    'restored_by': restored_by
                }
                
                AuditLog.log_action(
                    session, restored_by, "document_restore", "documents", document_id,
                    None, restore_info
                )
                
                self.logger.info(f"تم استعادة الوثيقة: {document.title} (ID: {document_id})")
                return True
                
        except Exception as e:
            self.logger.error(f"خطأ في استعادة الوثيقة: {e}")
            return False
    
    def get_trash_documents(self, user_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Get list of documents in trash
        
        Args:
            user_id: Filter by user (None for all)
            
        Returns:
            List of trash documents
        """
        try:
            with self.db_manager.get_session() as session:
                # Query deleted documents
                query = session.query(Document).filter(Document.is_active == False)
                
                if user_id:
                    query = query.filter(Document.created_by == user_id)
                
                deleted_docs = query.order_by(Document.updated_at.desc()).all()
                
                # Get trash metadata
                trash_docs = []
                for doc in deleted_docs:
                    trash_record = self._get_trash_record(session, doc.id)
                    
                    doc_dict = doc.to_dict()
                    if trash_record:
                        doc_dict.update(trash_record)
                    
                    trash_docs.append(doc_dict)
                
                return trash_docs
                
        except Exception as e:
            self.logger.error(f"خطأ في جلب وثائق سلة المهملات: {e}")
            return []
    
    def empty_trash(self, user_id: Optional[int] = None, older_than_days: Optional[int] = None) -> int:
        """
        Empty trash (permanent deletion)
        
        Args:
            user_id: Empty trash for specific user (None for all)
            older_than_days: Only delete items older than specified days
            
        Returns:
            Number of documents permanently deleted
        """
        try:
            with self.db_manager.get_session() as session:
                # Build query for deleted documents
                query = session.query(Document).filter(Document.is_active == False)
                
                if user_id:
                    query = query.filter(Document.created_by == user_id)
                
                if older_than_days:
                    cutoff_date = datetime.utcnow() - timedelta(days=older_than_days)
                    query = query.filter(Document.updated_at < cutoff_date)
                
                documents_to_delete = query.all()
                deleted_count = 0
                
                for document in documents_to_delete:
                    # Delete physical files
                    self._delete_physical_files(document)
                    
                    # Remove from database
                    session.delete(document)
                    
                    # Remove trash record
                    self._remove_trash_record(session, document.id)
                    
                    deleted_count += 1
                
                session.commit()
                
                self.logger.info(f"تم حذف {deleted_count} وثيقة نهائياً من سلة المهملات")
                return deleted_count
                
        except Exception as e:
            self.logger.error(f"خطأ في إفراغ سلة المهملات: {e}")
            return 0
    
    def auto_cleanup_trash(self) -> int:
        """
        Automatically clean up old trash items
        
        Returns:
            Number of documents cleaned up
        """
        return self.empty_trash(older_than_days=self.auto_cleanup_days)
    
    def _create_trash_record(self, session: Session, document: Document, deletion_info: Dict[str, Any]):
        """Create trash record for deleted document"""
        try:
            # Create trash table if not exists
            session.execute(text("""
                CREATE TABLE IF NOT EXISTS document_trash (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    document_id INTEGER NOT NULL,
                    deleted_at DATETIME NOT NULL,
                    deleted_by INTEGER NOT NULL,
                    deletion_reason TEXT,
                    deletion_type TEXT DEFAULT 'soft',
                    original_data TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """))
            
            # Insert trash record
            session.execute(text("""
                INSERT INTO document_trash 
                (document_id, deleted_at, deleted_by, deletion_reason, deletion_type, original_data)
                VALUES (?, ?, ?, ?, ?, ?)
            """), [
                document.id,
                deletion_info['deleted_at'],
                deletion_info['deleted_by'],
                deletion_info.get('deletion_reason'),
                deletion_info['deletion_type'],
                str(document.to_dict())
            ])
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء سجل سلة المهملات: {e}")
    
    def _get_trash_record(self, session: Session, document_id: int) -> Optional[Dict[str, Any]]:
        """Get trash record for document"""
        try:
            result = session.execute(text("""
                SELECT deleted_at, deleted_by, deletion_reason, deletion_type
                FROM document_trash
                WHERE document_id = ?
                ORDER BY created_at DESC
                LIMIT 1
            """), [document_id]).fetchone()
            
            if result:
                return {
                    'deleted_at': result[0],
                    'deleted_by': result[1],
                    'deletion_reason': result[2],
                    'deletion_type': result[3]
                }
            
            return None
            
        except Exception as e:
            self.logger.error(f"خطأ في جلب سجل سلة المهملات: {e}")
            return None
    
    def _remove_trash_record(self, session: Session, document_id: int):
        """Remove trash record for document"""
        try:
            session.execute(text("""
                DELETE FROM document_trash WHERE document_id = ?
            """), [document_id])
            
        except Exception as e:
            self.logger.error(f"خطأ في حذف سجل سلة المهملات: {e}")
    
    def _delete_physical_files(self, document: Document):
        """Delete physical files associated with document"""
        try:
            # Delete main file
            file_path = Path(document.file_path)
            if file_path.exists():
                if SEND2TRASH_AVAILABLE:
                    # Use system trash if available
                    send2trash(str(file_path))
                else:
                    # Direct deletion
                    file_path.unlink()
            
            # Delete thumbnail
            if document.thumbnail_path:
                thumbnail_path = Path(document.thumbnail_path)
                if thumbnail_path.exists():
                    if SEND2TRASH_AVAILABLE:
                        send2trash(str(thumbnail_path))
                    else:
                        thumbnail_path.unlink()
            
        except Exception as e:
            self.logger.error(f"خطأ في حذف الملفات الفيزيائية: {e}")
    
    def get_trash_statistics(self) -> Dict[str, Any]:
        """Get trash statistics"""
        try:
            with self.db_manager.get_session() as session:
                # Count deleted documents
                total_deleted = session.query(Document).filter(Document.is_active == False).count()
                
                # Calculate total size
                deleted_docs = session.query(Document).filter(Document.is_active == False).all()
                total_size = sum(doc.file_size for doc in deleted_docs)
                
                # Count by age
                week_ago = datetime.utcnow() - timedelta(days=7)
                month_ago = datetime.utcnow() - timedelta(days=30)
                
                recent_count = session.query(Document).filter(
                    Document.is_active == False,
                    Document.updated_at >= week_ago
                ).count()
                
                old_count = session.query(Document).filter(
                    Document.is_active == False,
                    Document.updated_at < month_ago
                ).count()
                
                return {
                    'total_documents': total_deleted,
                    'total_size_bytes': total_size,
                    'total_size_mb': round(total_size / (1024 * 1024), 2),
                    'recent_count': recent_count,
                    'old_count': old_count,
                    'auto_cleanup_days': self.auto_cleanup_days
                }
                
        except Exception as e:
            self.logger.error(f"خطأ في حساب إحصائيات سلة المهملات: {e}")
            return {
                'total_documents': 0,
                'total_size_bytes': 0,
                'total_size_mb': 0,
                'recent_count': 0,
                'old_count': 0,
                'auto_cleanup_days': self.auto_cleanup_days
            }
