"""
Advanced Features Tests for Electronic Archive System
اختبارات الميزات المتقدمة لنظام الأرشفة الإلكترونية

Tests for all advanced features including FTS5, themes, scanning, etc.
"""

import pytest
import sys
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.config import Config
from src.core.database_sqlite import DatabaseManagerSQLite
from src.ui.theme_manager import ThemeManager, ThemeType
from src.services.search_fts5 import SearchServiceFTS5
from src.services.preview_service import PreviewService
from src.services.scanning_service import ScanningService
from src.services.export_service import ExportService
from src.services.trash_service import TrashService


class TestThemeManager:
    """Test theme management functionality"""
    
    def test_theme_manager_creation(self):
        """Test theme manager creation"""
        theme_manager = ThemeManager()
        assert theme_manager is not None
        assert theme_manager.current_theme in [ThemeType.LIGHT, ThemeType.DARK, ThemeType.AUTO]
    
    def test_theme_switching(self):
        """Test theme switching"""
        theme_manager = ThemeManager()
        
        # Test setting light theme
        theme_manager.set_theme(ThemeType.LIGHT)
        assert theme_manager.get_current_theme() == ThemeType.LIGHT
        
        # Test setting dark theme
        theme_manager.set_theme(ThemeType.DARK)
        assert theme_manager.get_current_theme() == ThemeType.DARK
        
        # Test toggle
        theme_manager.toggle_theme()
        assert theme_manager.get_current_theme() == ThemeType.LIGHT
    
    def test_theme_icon_paths(self):
        """Test theme-appropriate icon paths"""
        theme_manager = ThemeManager()
        
        theme_manager.set_theme(ThemeType.LIGHT)
        icon_path = theme_manager.get_theme_icon("document")
        assert "light" in icon_path
        
        theme_manager.set_theme(ThemeType.DARK)
        icon_path = theme_manager.get_theme_icon("document")
        assert "dark" in icon_path


class TestSearchServiceFTS5:
    """Test FTS5 search functionality"""
    
    @pytest.fixture
    def db_manager(self):
        """Create test database manager"""
        config = Config()
        db_manager = DatabaseManagerSQLite(config)
        db_manager.db_path = Path(":memory:")  # Use in-memory database
        db_manager.initialize()
        return db_manager
    
    @pytest.fixture
    def search_service(self, db_manager):
        """Create search service"""
        return SearchServiceFTS5(db_manager)
    
    def test_search_service_creation(self, search_service):
        """Test search service creation"""
        assert search_service is not None
    
    def test_fts_query_preparation(self, search_service):
        """Test FTS query preparation"""
        # Single word
        query = search_service._prepare_fts_query("test")
        assert '"test"*' in query
        
        # Multiple words
        query = search_service._prepare_fts_query("test document")
        assert "AND" in query
        
        # Quoted phrase
        query = search_service._prepare_fts_query('"test document"')
        assert query == '"test document"'
    
    def test_search_suggestions(self, search_service):
        """Test search suggestions"""
        suggestions = search_service.get_search_suggestions("test")
        assert isinstance(suggestions, list)
    
    def test_empty_search(self, search_service):
        """Test search with empty query"""
        results, count = search_service.search_documents("")
        assert isinstance(results, list)
        assert isinstance(count, int)


class TestPreviewService:
    """Test preview and thumbnail functionality"""
    
    @pytest.fixture
    def preview_service(self):
        """Create preview service"""
        config = Config()
        return PreviewService(config)
    
    def test_preview_service_creation(self, preview_service):
        """Test preview service creation"""
        assert preview_service is not None
        assert preview_service.thumbnail_size == (200, 200)
        assert preview_service.preview_size == (800, 600)
    
    def test_supported_formats(self, preview_service):
        """Test supported file formats"""
        formats = preview_service.get_supported_formats()
        assert isinstance(formats, list)
        assert '.jpg' in formats
        assert '.pdf' in formats
    
    def test_previewable_check(self, preview_service):
        """Test previewable file check"""
        # Image file
        image_path = Path("test.jpg")
        assert preview_service.is_previewable(image_path) == True
        
        # PDF file
        pdf_path = Path("test.pdf")
        assert preview_service.is_previewable(pdf_path) == True
        
        # Non-previewable file
        text_path = Path("test.txt")
        assert preview_service.is_previewable(text_path) == False
    
    def test_thumbnail_stats(self, preview_service):
        """Test thumbnail statistics"""
        stats = preview_service.get_thumbnail_stats()
        assert isinstance(stats, dict)
        assert 'thumbnail_count' in stats
        assert 'total_size_mb' in stats


class TestScanningService:
    """Test scanning functionality"""
    
    @pytest.fixture
    def scanning_service(self):
        """Create scanning service"""
        config = Config()
        return ScanningService(config)
    
    def test_scanning_service_creation(self, scanning_service):
        """Test scanning service creation"""
        assert scanning_service is not None
        assert scanning_service.default_dpi == 300
    
    def test_wia_availability_check(self, scanning_service):
        """Test WIA availability check"""
        # This will be False on non-Windows systems
        wia_available = scanning_service.wia_available
        assert isinstance(wia_available, bool)
    
    def test_scan_settings_template(self, scanning_service):
        """Test scan settings template"""
        settings = scanning_service.get_scan_settings_template()
        assert isinstance(settings, dict)
        assert 'dpi' in settings
        assert 'color_mode' in settings
        assert 'format' in settings
    
    @patch('sys.platform', 'win32')
    def test_get_available_scanners_windows(self, scanning_service):
        """Test getting available scanners on Windows"""
        # Mock the scanner detection
        with patch.object(scanning_service, 'wia_available', True):
            with patch('comtypes.client.CreateObject'):
                scanners = scanning_service.get_available_scanners()
                assert isinstance(scanners, list)
    
    def test_cleanup_temp_files(self, scanning_service):
        """Test cleanup of temporary files"""
        # This should not raise an exception
        scanning_service.cleanup_temp_files()


class TestExportService:
    """Test export and print functionality"""
    
    @pytest.fixture
    def export_service(self):
        """Create export service"""
        config = Config()
        return ExportService(config)
    
    @pytest.fixture
    def sample_documents(self):
        """Create sample documents for testing"""
        return [
            {
                'id': 1,
                'title': 'Test Document 1',
                'description': 'Test description',
                'file_path': '/path/to/test1.pdf',
                'file_type': 'pdf',
                'file_size': 1024000,
                'created_at': '2025-01-01',
                'tags': 'test, document'
            },
            {
                'id': 2,
                'title': 'Test Document 2',
                'description': 'Another test',
                'file_path': '/path/to/test2.docx',
                'file_type': 'docx',
                'file_size': 512000,
                'created_at': '2025-01-02',
                'tags': 'test, office'
            }
        ]
    
    def test_export_service_creation(self, export_service):
        """Test export service creation"""
        assert export_service is not None
    
    def test_filename_sanitization(self, export_service):
        """Test filename sanitization"""
        # Test with invalid characters
        sanitized = export_service._sanitize_filename("test<>file|name")
        assert "<" not in sanitized
        assert ">" not in sanitized
        assert "|" not in sanitized
        
        # Test with long filename
        long_name = "a" * 250
        sanitized = export_service._sanitize_filename(long_name)
        assert len(sanitized) <= 200
    
    def test_metadata_creation(self, export_service, sample_documents):
        """Test metadata text creation"""
        metadata = export_service._create_metadata_text(sample_documents[0])
        assert isinstance(metadata, str)
        assert "Test Document 1" in metadata
        assert "pdf" in metadata
    
    def test_export_summary_creation(self, export_service, sample_documents):
        """Test export summary creation"""
        summary = export_service._create_export_summary(sample_documents)
        assert isinstance(summary, str)
        assert "2" in summary  # Document count
        assert "Test Document 1" in summary
    
    def test_file_size_formatting(self, export_service):
        """Test file size formatting"""
        # Test bytes
        formatted = export_service._format_file_size(512)
        assert "512.0 B" in formatted
        
        # Test KB
        formatted = export_service._format_file_size(1024)
        assert "1.0 KB" in formatted
        
        # Test MB
        formatted = export_service._format_file_size(1024 * 1024)
        assert "1.0 MB" in formatted


class TestTrashService:
    """Test trash/recycle bin functionality"""
    
    @pytest.fixture
    def db_manager(self):
        """Create test database manager"""
        config = Config()
        db_manager = DatabaseManagerSQLite(config)
        db_manager.db_path = Path(":memory:")
        db_manager.initialize()
        return db_manager
    
    @pytest.fixture
    def trash_service(self, db_manager):
        """Create trash service"""
        return TrashService(db_manager)
    
    def test_trash_service_creation(self, trash_service):
        """Test trash service creation"""
        assert trash_service is not None
        assert trash_service.auto_cleanup_days == 30
    
    def test_trash_statistics(self, trash_service):
        """Test trash statistics"""
        stats = trash_service.get_trash_statistics()
        assert isinstance(stats, dict)
        assert 'total_documents' in stats
        assert 'total_size_mb' in stats
        assert 'auto_cleanup_days' in stats
    
    def test_get_trash_documents(self, trash_service):
        """Test getting trash documents"""
        trash_docs = trash_service.get_trash_documents()
        assert isinstance(trash_docs, list)


class TestIntegration:
    """Integration tests for advanced features"""
    
    def test_all_services_integration(self):
        """Test that all services can be created together"""
        config = Config()
        db_manager = DatabaseManagerSQLite(config)
        db_manager.db_path = Path(":memory:")
        db_manager.initialize()
        
        # Create all services
        services = {
            'search': SearchServiceFTS5(db_manager),
            'preview': PreviewService(config),
            'scanning': ScanningService(config),
            'export': ExportService(config),
            'trash': TrashService(db_manager)
        }
        
        # Verify all services are created
        assert len(services) == 5
        for service_name, service in services.items():
            assert service is not None
    
    def test_theme_manager_singleton(self):
        """Test theme manager singleton behavior"""
        from src.ui.theme_manager import theme_manager
        
        # Get the global instance
        tm1 = theme_manager
        tm2 = theme_manager
        
        # Should be the same instance
        assert tm1 is tm2


# Test fixtures for pytest-qt if available
try:
    import pytest_qt
    
    class TestUIComponents:
        """Test UI components with pytest-qt"""
        
        def test_theme_application(self, qtbot):
            """Test theme application to widgets"""
            from PySide6.QtWidgets import QWidget
            
            widget = QWidget()
            qtbot.addWidget(widget)
            
            # Apply theme
            theme_manager = ThemeManager()
            theme_manager._apply_theme()
            
            # Widget should have stylesheet applied
            assert widget.styleSheet() is not None or True  # Theme might not apply to test widget

except ImportError:
    # pytest-qt not available, skip UI tests
    pass


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v", "--tb=short"])
