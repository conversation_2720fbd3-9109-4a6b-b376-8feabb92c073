"""
Advanced Search Service with SQLite FTS5
خدمة البحث المتقدم مع SQLite FTS5

Full-text search with highlighting and advanced filtering
"""

import logging
import sqlite3
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
from sqlalchemy import text
from sqlalchemy.orm import Session

from ..core.database_sqlite import DatabaseManagerSQLite


class SearchServiceFTS5:
    """
    Advanced search service using SQLite FTS5
    خدمة البحث المتقدم باستخدام SQLite FTS5
    """
    
    def __init__(self, db_manager: DatabaseManagerSQLite):
        """
        Initialize FTS5 search service
        
        Args:
            db_manager: SQLite database manager
        """
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)
        self._setup_fts5()
    
    def _setup_fts5(self):
        """Setup FTS5 virtual table for full-text search"""
        try:
            with self.db_manager.get_session() as session:
                # Create FTS5 virtual table for documents
                session.execute(text("""
                    CREATE VIRTUAL TABLE IF NOT EXISTS documents_fts USING fts5(
                        title,
                        description,
                        full_text_content,
                        tags,
                        file_name,
                        content='documents',
                        content_rowid='id'
                    )
                """))
                
                # Create triggers to keep FTS5 table in sync
                session.execute(text("""
                    CREATE TRIGGER IF NOT EXISTS documents_ai AFTER INSERT ON documents BEGIN
                        INSERT INTO documents_fts(rowid, title, description, full_text_content, tags, file_name)
                        VALUES (new.id, new.title, new.description, new.full_text_content, new.tags, new.file_name);
                    END
                """))
                
                session.execute(text("""
                    CREATE TRIGGER IF NOT EXISTS documents_ad AFTER DELETE ON documents BEGIN
                        INSERT INTO documents_fts(documents_fts, rowid, title, description, full_text_content, tags, file_name)
                        VALUES('delete', old.id, old.title, old.description, old.full_text_content, old.tags, old.file_name);
                    END
                """))
                
                session.execute(text("""
                    CREATE TRIGGER IF NOT EXISTS documents_au AFTER UPDATE ON documents BEGIN
                        INSERT INTO documents_fts(documents_fts, rowid, title, description, full_text_content, tags, file_name)
                        VALUES('delete', old.id, old.title, old.description, old.full_text_content, old.tags, old.file_name);
                        INSERT INTO documents_fts(rowid, title, description, full_text_content, tags, file_name)
                        VALUES (new.id, new.title, new.description, new.full_text_content, new.tags, new.file_name);
                    END
                """))
                
                session.commit()
                
                # Rebuild FTS5 index if needed
                self._rebuild_fts_index()
                
                self.logger.info("تم إعداد FTS5 بنجاح")
                
        except Exception as e:
            self.logger.error(f"خطأ في إعداد FTS5: {e}")
    
    def _rebuild_fts_index(self):
        """Rebuild FTS5 index from existing documents"""
        try:
            with self.db_manager.get_session() as session:
                # Check if FTS table is empty
                result = session.execute(text("SELECT COUNT(*) FROM documents_fts")).fetchone()
                fts_count = result[0] if result else 0
                
                # Check documents table count
                result = session.execute(text("SELECT COUNT(*) FROM documents WHERE is_active = 1")).fetchone()
                docs_count = result[0] if result else 0
                
                # Rebuild if counts don't match
                if fts_count != docs_count:
                    self.logger.info("إعادة بناء فهرس FTS5...")
                    session.execute(text("INSERT INTO documents_fts(documents_fts) VALUES('rebuild')"))
                    session.commit()
                    self.logger.info("تم إعادة بناء فهرس FTS5")
                
        except Exception as e:
            self.logger.error(f"خطأ في إعادة بناء فهرس FTS5: {e}")
    
    def search_documents(self, query: str, filters: Optional[Dict[str, Any]] = None,
                        limit: int = 100, offset: int = 0) -> Tuple[List[Dict], int]:
        """
        Search documents using FTS5
        
        Args:
            query: Search query
            filters: Additional filters
            limit: Maximum results
            offset: Offset for pagination
            
        Returns:
            Tuple of (results, total_count)
        """
        try:
            if not query or not query.strip():
                return self._get_all_documents(filters, limit, offset)
            
            # Prepare FTS5 query
            fts_query = self._prepare_fts_query(query)
            
            with self.db_manager.get_session() as session:
                # Build search query with highlighting
                search_sql = """
                    SELECT 
                        d.id,
                        d.title,
                        d.description,
                        d.file_name,
                        d.file_type,
                        d.file_size,
                        d.created_at,
                        d.document_date,
                        d.tags,
                        d.thumbnail_path,
                        fts.rank,
                        highlight(documents_fts, 0, '<mark>', '</mark>') as title_highlight,
                        highlight(documents_fts, 1, '<mark>', '</mark>') as description_highlight,
                        snippet(documents_fts, 2, '<mark>', '</mark>', '...', 32) as content_snippet
                    FROM documents_fts fts
                    JOIN documents d ON d.id = fts.rowid
                    WHERE documents_fts MATCH ? AND d.is_active = 1
                """
                
                # Add filters
                params = [fts_query]
                if filters:
                    search_sql, params = self._apply_filters(search_sql, params, filters)
                
                # Add ordering and pagination
                search_sql += " ORDER BY fts.rank LIMIT ? OFFSET ?"
                params.extend([limit, offset])
                
                # Execute search
                results = session.execute(text(search_sql), params).fetchall()
                
                # Get total count
                count_sql = """
                    SELECT COUNT(*)
                    FROM documents_fts fts
                    JOIN documents d ON d.id = fts.rowid
                    WHERE documents_fts MATCH ? AND d.is_active = 1
                """
                count_params = [fts_query]
                if filters:
                    count_sql, count_params = self._apply_filters(count_sql, count_params, filters)
                
                total_count = session.execute(text(count_sql), count_params).fetchone()[0]
                
                # Convert results to dictionaries
                documents = []
                for row in results:
                    doc = {
                        'id': row[0],
                        'title': row[1],
                        'description': row[2],
                        'file_name': row[3],
                        'file_type': row[4],
                        'file_size': row[5],
                        'created_at': row[6],
                        'document_date': row[7],
                        'tags': row[8],
                        'thumbnail_path': row[9],
                        'rank': row[10],
                        'title_highlight': row[11],
                        'description_highlight': row[12],
                        'content_snippet': row[13]
                    }
                    documents.append(doc)
                
                return documents, total_count
                
        except Exception as e:
            self.logger.error(f"خطأ في البحث: {e}")
            return [], 0
    
    def _prepare_fts_query(self, query: str) -> str:
        """
        Prepare query for FTS5
        
        Args:
            query: Raw search query
            
        Returns:
            FTS5-formatted query
        """
        # Clean and tokenize query
        words = query.strip().split()
        
        # Handle different query types
        if len(words) == 1:
            # Single word - use prefix matching
            return f'"{words[0]}"*'
        else:
            # Multiple words - use phrase or AND search
            if '"' in query:
                # Already quoted phrase
                return query
            else:
                # Convert to AND search
                return ' AND '.join([f'"{word}"*' for word in words])
    
    def _apply_filters(self, sql: str, params: List, filters: Dict[str, Any]) -> Tuple[str, List]:
        """Apply additional filters to search query"""
        conditions = []
        
        if filters.get('category_id'):
            conditions.append("d.category_id = ?")
            params.append(filters['category_id'])
        
        if filters.get('file_type'):
            conditions.append("d.file_type = ?")
            params.append(filters['file_type'])
        
        if filters.get('date_from'):
            conditions.append("d.document_date >= ?")
            params.append(filters['date_from'])
        
        if filters.get('date_to'):
            conditions.append("d.document_date <= ?")
            params.append(filters['date_to'])
        
        if filters.get('created_by'):
            conditions.append("d.created_by = ?")
            params.append(filters['created_by'])
        
        if conditions:
            sql += " AND " + " AND ".join(conditions)
        
        return sql, params
    
    def _get_all_documents(self, filters: Optional[Dict[str, Any]] = None,
                          limit: int = 100, offset: int = 0) -> Tuple[List[Dict], int]:
        """Get all documents when no search query is provided"""
        try:
            with self.db_manager.get_session() as session:
                sql = """
                    SELECT 
                        id, title, description, file_name, file_type, file_size,
                        created_at, document_date, tags, thumbnail_path
                    FROM documents
                    WHERE is_active = 1
                """
                
                params = []
                if filters:
                    sql, params = self._apply_filters(sql, params, filters)
                
                sql += " ORDER BY created_at DESC LIMIT ? OFFSET ?"
                params.extend([limit, offset])
                
                results = session.execute(text(sql), params).fetchall()
                
                # Get total count
                count_sql = "SELECT COUNT(*) FROM documents WHERE is_active = 1"
                count_params = []
                if filters:
                    count_sql, count_params = self._apply_filters(count_sql, count_params, filters)
                
                total_count = session.execute(text(count_sql), count_params).fetchone()[0]
                
                # Convert to dictionaries
                documents = []
                for row in results:
                    doc = {
                        'id': row[0],
                        'title': row[1],
                        'description': row[2],
                        'file_name': row[3],
                        'file_type': row[4],
                        'file_size': row[5],
                        'created_at': row[6],
                        'document_date': row[7],
                        'tags': row[8],
                        'thumbnail_path': row[9],
                        'rank': 0,
                        'title_highlight': row[1],
                        'description_highlight': row[2],
                        'content_snippet': ''
                    }
                    documents.append(doc)
                
                return documents, total_count
                
        except Exception as e:
            self.logger.error(f"خطأ في جلب الوثائق: {e}")
            return [], 0
    
    def get_search_suggestions(self, partial_query: str, limit: int = 10) -> List[str]:
        """
        Get search suggestions based on partial query
        
        Args:
            partial_query: Partial search query
            limit: Maximum suggestions
            
        Returns:
            List of search suggestions
        """
        try:
            if not partial_query or len(partial_query) < 2:
                return []
            
            with self.db_manager.get_session() as session:
                # Search in titles and tags for suggestions
                sql = """
                    SELECT DISTINCT title
                    FROM documents
                    WHERE title LIKE ? AND is_active = 1
                    LIMIT ?
                """
                
                pattern = f"%{partial_query}%"
                results = session.execute(text(sql), [pattern, limit]).fetchall()
                
                suggestions = [row[0] for row in results]
                
                # Also search in tags
                tag_sql = """
                    SELECT DISTINCT tags
                    FROM documents
                    WHERE tags LIKE ? AND is_active = 1 AND tags IS NOT NULL
                    LIMIT ?
                """
                
                tag_results = session.execute(text(tag_sql), [pattern, limit]).fetchall()
                
                # Extract individual tags
                for row in tag_results:
                    if row[0]:
                        tags = [tag.strip() for tag in row[0].split(',')]
                        matching_tags = [tag for tag in tags if partial_query.lower() in tag.lower()]
                        suggestions.extend(matching_tags)
                
                # Remove duplicates and limit
                unique_suggestions = list(set(suggestions))[:limit]
                
                return unique_suggestions
                
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على اقتراحات البحث: {e}")
            return []
    
    def optimize_fts_index(self):
        """Optimize FTS5 index for better performance"""
        try:
            with self.db_manager.get_session() as session:
                session.execute(text("INSERT INTO documents_fts(documents_fts) VALUES('optimize')"))
                session.commit()
                self.logger.info("تم تحسين فهرس FTS5")
                
        except Exception as e:
            self.logger.error(f"خطأ في تحسين فهرس FTS5: {e}")
