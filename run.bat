@echo off
REM Batch file to run Electronic Archive System on Windows
REM ملف تشغيل نظام الأرشفة الإلكترونية على Windows

echo ========================================
echo Electronic Archive System
echo نظام الأرشفة الإلكترونية
echo ========================================

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Python is not installed or not in PATH
    echo Python غير مثبت أو غير موجود في PATH
    pause
    exit /b 1
)

REM Check if virtual environment exists
if not exist "venv" (
    echo Creating virtual environment...
    echo إنشاء البيئة الافتراضية...
    python -m venv venv
)

REM Activate virtual environment
echo Activating virtual environment...
echo تفعيل البيئة الافتراضية...
call venv\Scripts\activate.bat

REM Install requirements
echo Installing requirements...
echo تثبيت المتطلبات...
pip install -r requirements.txt

REM Check if database is set up
if not exist "config.ini" (
    echo Configuration file not found. Running database setup...
    echo ملف التكوين غير موجود. تشغيل إعداد قاعدة البيانات...
    python setup_database.py
)

REM Run the application
echo Starting Electronic Archive System...
echo بدء تشغيل نظام الأرشفة الإلكترونية...
python main.py

pause
