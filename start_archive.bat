@echo off
chcp 65001 >nul
title نظام الأرشفة الإلكترونية - Electronic Archive System

echo ========================================
echo نظام الأرشفة الإلكترونية
echo Electronic Archive System
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo ❌ Python is not installed or not in PATH
    echo.
    echo يرجى تثبيت Python 3.11+ من:
    echo Please install Python 3.11+ from:
    echo https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo ✅ Python متوفر
echo ✅ Python available
echo.

REM Check for quick launcher
if exist "quick_archive.py" (
    echo 🚀 تشغيل المشغل السريع...
    echo 🚀 Starting quick launcher...
    python quick_archive.py
    goto :end
)

REM Try different applications in order of preference
echo 🔍 البحث عن التطبيقات المتاحة...
echo 🔍 Looking for available applications...
echo.

if exist "archive_system.py" (
    echo ✅ تم العثور على النظام المتكامل
    echo ✅ Found Complete System
    echo 🚀 تشغيل النظام المتكامل...
    echo 🚀 Starting Complete System...
    python archive_system.py
    goto :end
)

if exist "advanced_app.py" (
    echo ✅ تم العثور على النظام المتقدم
    echo ✅ Found Advanced System
    echo 🚀 تشغيل النظام المتقدم...
    echo 🚀 Starting Advanced System...
    python advanced_app.py
    goto :end
)

if exist "main_simple.py" (
    echo ✅ تم العثور على النظام المبسط
    echo ✅ Found Simple System
    echo 🚀 تشغيل النظام المبسط...
    echo 🚀 Starting Simple System...
    python main_simple.py
    goto :end
)

if exist "demo_app.py" (
    echo ✅ تم العثور على النظام التجريبي
    echo ✅ Found Demo System
    echo 🚀 تشغيل النظام التجريبي...
    echo 🚀 Starting Demo System...
    python demo_app.py
    goto :end
)

if exist "test_app.py" (
    echo ✅ تم العثور على تطبيق الاختبار
    echo ✅ Found Test Application
    echo 🚀 تشغيل تطبيق الاختبار...
    echo 🚀 Starting Test Application...
    python test_app.py
    goto :end
)

if exist "main.py" (
    echo ✅ تم العثور على التطبيق الرئيسي
    echo ✅ Found Main Application
    echo 🚀 تشغيل التطبيق الرئيسي...
    echo 🚀 Starting Main Application...
    python main.py
    goto :end
)

REM No applications found
echo ❌ لم يتم العثور على أي تطبيق
echo ❌ No applications found
echo.
echo يرجى التأكد من وجود ملفات التطبيق في هذا المجلد:
echo Please ensure application files exist in this folder:
echo %CD%
echo.
echo الملفات المطلوبة:
echo Required files:
echo - archive_system.py (النظام المتكامل)
echo - advanced_app.py (النظام المتقدم)
echo - main_simple.py (النظام المبسط)
echo - demo_app.py (النظام التجريبي)
echo - test_app.py (تطبيق الاختبار)
echo.

:end
echo.
echo ========================================
echo شكراً لاستخدام نظام الأرشفة الإلكترونية
echo Thank you for using Electronic Archive System
echo ========================================
pause
