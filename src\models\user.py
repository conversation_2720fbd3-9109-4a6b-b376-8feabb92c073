"""
User Model for Electronic Archive System
نموذج المستخدم لنظام الأرشفة الإلكترونية

Handles user authentication, roles, and permissions
"""

from datetime import datetime
from typing import Optional, List
from sqlalchemy import Column, String, Boolean, Integer, DateTime, Enum as SQLEnum
from sqlalchemy.orm import relationship, Session
from sqlalchemy.sql import func
import bcrypt
import enum

from .base import BaseModel


class UserRole(enum.Enum):
    """أدوار المستخدمين"""
    ADMIN = "admin"
    MANAGER = "manager"
    USER = "user"
    VIEWER = "viewer"


class User(BaseModel):
    """
    User model for authentication and authorization
    نموذج المستخدم للمصادقة والتخويل
    """
    __tablename__ = "users"
    
    username = Column(String(50), unique=True, nullable=False, index=True)
    email = Column(String(100), unique=True, nullable=False, index=True)
    password_hash = Column(String(255), nullable=False)
    full_name = Column(String(100), nullable=True)
    role = Column(SQLEnum(UserRole), default=UserRole.USER, nullable=False)
    last_login = Column(DateTime, nullable=True)
    login_attempts = Column(Integer, default=0)
    is_locked = Column(Boolean, default=False)
    
    # Relationships
    documents = relationship("Document", back_populates="created_by_user", lazy="dynamic")
    audit_logs = relationship("AuditLog", back_populates="user", lazy="dynamic")
    
    def set_password(self, password: str):
        """
        Set user password with bcrypt hashing
        
        Args:
            password: Plain text password
        """
        salt = bcrypt.gensalt()
        self.password_hash = bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')
    
    def check_password(self, password: str) -> bool:
        """
        Check if provided password matches stored hash
        
        Args:
            password: Plain text password to check
            
        Returns:
            True if password matches
        """
        return bcrypt.checkpw(
            password.encode('utf-8'),
            self.password_hash.encode('utf-8')
        )
    
    def update_last_login(self, session: Session):
        """
        Update last login timestamp
        
        Args:
            session: Database session
        """
        self.last_login = datetime.utcnow()
        self.login_attempts = 0
        self.save(session)
    
    def increment_login_attempts(self, session: Session, max_attempts: int = 5):
        """
        Increment failed login attempts and lock account if needed
        
        Args:
            session: Database session
            max_attempts: Maximum allowed login attempts
        """
        self.login_attempts += 1
        if self.login_attempts >= max_attempts:
            self.is_locked = True
        self.save(session)
    
    def unlock_account(self, session: Session):
        """
        Unlock user account
        
        Args:
            session: Database session
        """
        self.is_locked = False
        self.login_attempts = 0
        self.save(session)
    
    def has_permission(self, permission: str) -> bool:
        """
        Check if user has specific permission
        
        Args:
            permission: Permission to check
            
        Returns:
            True if user has permission
        """
        permissions = {
            UserRole.ADMIN: [
                'create_user', 'edit_user', 'delete_user', 'view_user',
                'create_document', 'edit_document', 'delete_document', 'view_document',
                'create_category', 'edit_category', 'delete_category', 'view_category',
                'view_audit_log', 'system_settings', 'backup_restore'
            ],
            UserRole.MANAGER: [
                'create_document', 'edit_document', 'delete_document', 'view_document',
                'create_category', 'edit_category', 'delete_category', 'view_category',
                'view_audit_log'
            ],
            UserRole.USER: [
                'create_document', 'edit_own_document', 'view_document',
                'view_category'
            ],
            UserRole.VIEWER: [
                'view_document', 'view_category'
            ]
        }
        
        user_permissions = permissions.get(self.role, [])
        return permission in user_permissions
    
    def can_edit_document(self, document) -> bool:
        """
        Check if user can edit specific document
        
        Args:
            document: Document instance
            
        Returns:
            True if user can edit document
        """
        if self.role in [UserRole.ADMIN, UserRole.MANAGER]:
            return True
        
        if self.role == UserRole.USER and document.created_by == self.id:
            return True
        
        return False
    
    @classmethod
    def authenticate(cls, session: Session, username: str, password: str) -> Optional['User']:
        """
        Authenticate user with username and password
        
        Args:
            session: Database session
            username: Username or email
            password: Plain text password
            
        Returns:
            User instance if authentication successful, None otherwise
        """
        # Try to find user by username or email
        user = session.query(cls).filter(
            (cls.username == username) | (cls.email == username),
            cls.is_active == True
        ).first()
        
        if not user:
            return None
        
        if user.is_locked:
            return None
        
        if user.check_password(password):
            user.update_last_login(session)
            return user
        else:
            user.increment_login_attempts(session)
            return None
    
    @classmethod
    def create_admin_user(cls, session: Session, username: str, email: str, 
                         password: str, full_name: str = None) -> 'User':
        """
        Create admin user
        
        Args:
            session: Database session
            username: Username
            email: Email address
            password: Plain text password
            full_name: Full name
            
        Returns:
            Created admin user
        """
        admin_user = cls(
            username=username,
            email=email,
            full_name=full_name or "مدير النظام",
            role=UserRole.ADMIN
        )
        admin_user.set_password(password)
        admin_user.save(session)
        return admin_user
    
    @classmethod
    def get_by_username(cls, session: Session, username: str) -> Optional['User']:
        """
        Get user by username
        
        Args:
            session: Database session
            username: Username
            
        Returns:
            User instance or None
        """
        return session.query(cls).filter(
            cls.username == username,
            cls.is_active == True
        ).first()
    
    @classmethod
    def get_by_email(cls, session: Session, email: str) -> Optional['User']:
        """
        Get user by email
        
        Args:
            session: Database session
            email: Email address
            
        Returns:
            User instance or None
        """
        return session.query(cls).filter(
            cls.email == email,
            cls.is_active == True
        ).first()
    
    def to_dict(self, include_sensitive: bool = False) -> dict:
        """
        Convert user to dictionary
        
        Args:
            include_sensitive: Include sensitive information
            
        Returns:
            Dictionary representation
        """
        data = super().to_dict()
        
        if not include_sensitive:
            data.pop('password_hash', None)
            data.pop('login_attempts', None)
        
        # Convert enum to string
        data['role'] = self.role.value if self.role else None
        
        return data
    
    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', role='{self.role.value}')>"
