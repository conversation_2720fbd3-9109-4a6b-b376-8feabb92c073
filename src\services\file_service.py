"""
File Service for Electronic Archive System
خدمة الملفات لنظام الأرشفة الإلكترونية

Handles file operations, storage, and processing
"""

import os
import shutil
import hashlib
import mimetypes
from pathlib import Path
from typing import Optional, Dict, Any, List, Tuple
from datetime import datetime
import logging

from PIL import Image, ImageOps
import PyPDF2
from docx import Document as DocxDocument

from ..core.config import Config
from ..core.database import DatabaseManager
from ..models import Document


class FileService:
    """
    Service for file operations and management
    خدمة عمليات الملفات والإدارة
    """
    
    def __init__(self, config: Config, db_manager: DatabaseManager):
        """
        Initialize file service
        
        Args:
            config: Configuration manager
            db_manager: Database manager
        """
        self.config = config
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)
        
        # Get storage paths
        self.storage_paths = config.storage_paths
        
        # Ensure directories exist
        config.ensure_directories()
        
        # Supported file types
        self.supported_extensions = set(
            config.get('APPLICATION', 'supported_formats', '').split(',')
        )
        
        # Maximum file size (in bytes)
        max_size_str = config.get('APPLICATION', 'max_file_size', '100MB')
        self.max_file_size = self._parse_size_string(max_size_str)
    
    def _parse_size_string(self, size_str: str) -> int:
        """
        Parse size string (e.g., '100MB') to bytes
        
        Args:
            size_str: Size string
            
        Returns:
            Size in bytes
        """
        size_str = size_str.upper().strip()
        
        if size_str.endswith('KB'):
            return int(float(size_str[:-2]) * 1024)
        elif size_str.endswith('MB'):
            return int(float(size_str[:-2]) * 1024 * 1024)
        elif size_str.endswith('GB'):
            return int(float(size_str[:-2]) * 1024 * 1024 * 1024)
        else:
            return int(size_str)
    
    def validate_file(self, file_path: Path) -> Tuple[bool, str]:
        """
        Validate file for upload
        
        Args:
            file_path: Path to file
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        if not file_path.exists():
            return False, "الملف غير موجود"
        
        if not file_path.is_file():
            return False, "المسار المحدد ليس ملفاً"
        
        # Check file size
        file_size = file_path.stat().st_size
        if file_size > self.max_file_size:
            max_size_mb = self.max_file_size / (1024 * 1024)
            return False, f"حجم الملف كبير جداً. الحد الأقصى: {max_size_mb:.1f} ميجابايت"
        
        # Check file extension
        file_extension = file_path.suffix.lower().lstrip('.')
        if file_extension not in self.supported_extensions:
            return False, f"نوع الملف غير مدعوم: {file_extension}"
        
        return True, ""
    
    def store_file(self, source_path: Path, document_id: int, 
                   preserve_name: bool = True) -> Tuple[bool, str, Optional[str]]:
        """
        Store file in archive storage
        
        Args:
            source_path: Source file path
            document_id: Document ID
            preserve_name: Whether to preserve original filename
            
        Returns:
            Tuple of (success, error_message, stored_path)
        """
        try:
            # Validate file
            is_valid, error_msg = self.validate_file(source_path)
            if not is_valid:
                return False, error_msg, None
            
            # Generate storage path
            if preserve_name:
                filename = source_path.name
            else:
                extension = source_path.suffix
                filename = f"doc_{document_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}{extension}"
            
            # Create subdirectory based on date
            date_dir = datetime.now().strftime('%Y/%m')
            storage_dir = self.storage_paths['documents'] / date_dir
            storage_dir.mkdir(parents=True, exist_ok=True)
            
            # Full storage path
            storage_path = storage_dir / filename
            
            # Handle filename conflicts
            counter = 1
            original_storage_path = storage_path
            while storage_path.exists():
                name_part = original_storage_path.stem
                extension = original_storage_path.suffix
                storage_path = original_storage_path.parent / f"{name_part}_{counter}{extension}"
                counter += 1
            
            # Copy file
            shutil.copy2(source_path, storage_path)
            
            # Verify copy
            if not storage_path.exists():
                return False, "فشل في نسخ الملف", None
            
            self.logger.info(f"تم تخزين الملف: {storage_path}")
            return True, "", str(storage_path)
            
        except Exception as e:
            self.logger.error(f"خطأ في تخزين الملف: {e}")
            return False, f"خطأ في تخزين الملف: {e}", None
    
    def delete_file(self, file_path: str) -> bool:
        """
        Delete file from storage
        
        Args:
            file_path: Path to file to delete
            
        Returns:
            True if successful
        """
        try:
            path = Path(file_path)
            if path.exists():
                path.unlink()
                self.logger.info(f"تم حذف الملف: {file_path}")
                return True
            return True  # File doesn't exist, consider it deleted
            
        except Exception as e:
            self.logger.error(f"خطأ في حذف الملف: {e}")
            return False
    
    def get_file_info(self, file_path: Path) -> Dict[str, Any]:
        """
        Get file information
        
        Args:
            file_path: Path to file
            
        Returns:
            Dictionary with file information
        """
        try:
            stat = file_path.stat()
            
            # Get MIME type
            mime_type, _ = mimetypes.guess_type(str(file_path))
            
            return {
                'name': file_path.name,
                'size': stat.st_size,
                'extension': file_path.suffix.lower().lstrip('.'),
                'mime_type': mime_type,
                'created': datetime.fromtimestamp(stat.st_ctime),
                'modified': datetime.fromtimestamp(stat.st_mtime),
                'md5_hash': self.calculate_file_hash(file_path)
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على معلومات الملف: {e}")
            return {}
    
    def calculate_file_hash(self, file_path: Path) -> str:
        """
        Calculate MD5 hash of file
        
        Args:
            file_path: Path to file
            
        Returns:
            MD5 hash string
        """
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
            
        except Exception as e:
            self.logger.error(f"خطأ في حساب hash الملف: {e}")
            return ""
    
    def create_thumbnail(self, file_path: Path, document_id: int) -> Optional[str]:
        """
        Create thumbnail for file
        
        Args:
            file_path: Path to source file
            document_id: Document ID
            
        Returns:
            Path to thumbnail or None
        """
        try:
            extension = file_path.suffix.lower()
            
            # Create thumbnail directory
            thumbnail_dir = self.storage_paths['thumbnails']
            thumbnail_dir.mkdir(parents=True, exist_ok=True)
            
            thumbnail_path = thumbnail_dir / f"thumb_{document_id}.jpg"
            
            if extension in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff']:
                # Image thumbnail
                return self._create_image_thumbnail(file_path, thumbnail_path)
            
            elif extension == '.pdf':
                # PDF thumbnail
                return self._create_pdf_thumbnail(file_path, thumbnail_path)
            
            else:
                # No thumbnail for other file types
                return None
                
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء الصورة المصغرة: {e}")
            return None
    
    def _create_image_thumbnail(self, source_path: Path, thumbnail_path: Path) -> Optional[str]:
        """Create thumbnail for image file"""
        try:
            with Image.open(source_path) as img:
                # Convert to RGB if necessary
                if img.mode in ('RGBA', 'LA', 'P'):
                    img = img.convert('RGB')
                
                # Create thumbnail
                img.thumbnail((200, 200), Image.Resampling.LANCZOS)
                
                # Auto-orient based on EXIF data
                img = ImageOps.exif_transpose(img)
                
                # Save thumbnail
                img.save(thumbnail_path, 'JPEG', quality=85)
                
                return str(thumbnail_path)
                
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء صورة مصغرة للصورة: {e}")
            return None
    
    def _create_pdf_thumbnail(self, source_path: Path, thumbnail_path: Path) -> Optional[str]:
        """Create thumbnail for PDF file"""
        try:
            # This would require additional libraries like pdf2image
            # For now, return None
            # TODO: Implement PDF thumbnail generation
            return None
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء صورة مصغرة لـ PDF: {e}")
            return None
    
    def extract_text_content(self, file_path: Path) -> str:
        """
        Extract text content from file for indexing
        
        Args:
            file_path: Path to file
            
        Returns:
            Extracted text content
        """
        try:
            extension = file_path.suffix.lower()
            
            if extension == '.txt':
                return self._extract_text_from_txt(file_path)
            elif extension == '.pdf':
                return self._extract_text_from_pdf(file_path)
            elif extension in ['.doc', '.docx']:
                return self._extract_text_from_docx(file_path)
            else:
                return ""
                
        except Exception as e:
            self.logger.error(f"خطأ في استخراج النص: {e}")
            return ""
    
    def _extract_text_from_txt(self, file_path: Path) -> str:
        """Extract text from TXT file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except UnicodeDecodeError:
            # Try with different encoding
            with open(file_path, 'r', encoding='cp1256') as f:
                return f.read()
    
    def _extract_text_from_pdf(self, file_path: Path) -> str:
        """Extract text from PDF file"""
        try:
            text = ""
            with open(file_path, 'rb') as f:
                pdf_reader = PyPDF2.PdfReader(f)
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
            return text
        except Exception as e:
            self.logger.error(f"خطأ في استخراج النص من PDF: {e}")
            return ""
    
    def _extract_text_from_docx(self, file_path: Path) -> str:
        """Extract text from DOCX file"""
        try:
            doc = DocxDocument(file_path)
            text = ""
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            return text
        except Exception as e:
            self.logger.error(f"خطأ في استخراج النص من DOCX: {e}")
            return ""
    
    def get_storage_usage(self) -> Dict[str, Any]:
        """
        Get storage usage statistics
        
        Returns:
            Dictionary with storage usage information
        """
        try:
            usage = {}
            
            for name, path in self.storage_paths.items():
                if path.exists():
                    total_size = sum(
                        f.stat().st_size for f in path.rglob('*') if f.is_file()
                    )
                    file_count = sum(1 for f in path.rglob('*') if f.is_file())
                    
                    usage[name] = {
                        'size_bytes': total_size,
                        'size_mb': round(total_size / (1024 * 1024), 2),
                        'file_count': file_count
                    }
                else:
                    usage[name] = {
                        'size_bytes': 0,
                        'size_mb': 0,
                        'file_count': 0
                    }
            
            return usage
            
        except Exception as e:
            self.logger.error(f"خطأ في حساب استخدام التخزين: {e}")
            return {}
