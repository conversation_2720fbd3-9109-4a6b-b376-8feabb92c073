"""
SQLite Database Manager for Electronic Archive System
مدير قاعدة البيانات SQLite لنظام الأرشفة الإلكترونية

Alternative database manager using SQLite for testing and development
"""

import logging
from typing import Optional
from pathlib import Path
from sqlalchemy import create_engine, text, MetaData
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import SQLAlchemyError

from .config import Config


class DatabaseManagerSQLite:
    """مدير قاعدة البيانات SQLite"""
    
    def __init__(self, config: Config):
        """
        Initialize SQLite database manager
        
        Args:
            config: Configuration manager instance
        """
        self.config = config
        self.engine = None
        self.SessionLocal = None
        self.metadata = MetaData()
        self.logger = logging.getLogger(__name__)
        
        # SQLite database file path
        self.db_path = Path("archive.db")
    
    def _build_connection_string(self) -> str:
        """بناء سلسلة الاتصال بقاعدة البيانات SQLite"""
        return f"sqlite:///{self.db_path}"
    
    def initialize(self) -> bool:
        """
        Initialize database connection and create tables if needed
        
        Returns:
            True if successful, False otherwise
        """
        try:
            # Build connection string
            connection_string = self._build_connection_string()
            
            # Create engine
            self.engine = create_engine(
                connection_string,
                pool_pre_ping=True,
                echo=False  # Set to True for SQL debugging
            )
            
            # Test connection
            with self.engine.connect() as conn:
                result = conn.execute(text("SELECT 1"))
                result.fetchone()
            
            # Create session factory
            self.SessionLocal = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.engine
            )
            
            # Create database schema if needed
            self._create_database_schema()
            
            self.logger.info("تم تهيئة قاعدة البيانات SQLite بنجاح")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في تهيئة قاعدة البيانات: {e}")
            return False
    
    def _create_database_schema(self):
        """إنشاء مخطط قاعدة البيانات SQLite"""
        try:
            with self.engine.connect() as conn:
                # Create tables using raw SQL for SQLite
                
                # Users table
                conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS users (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        username TEXT UNIQUE NOT NULL,
                        email TEXT UNIQUE NOT NULL,
                        password_hash TEXT NOT NULL,
                        full_name TEXT,
                        role TEXT DEFAULT 'user',
                        is_active INTEGER DEFAULT 1,
                        last_login DATETIME,
                        login_attempts INTEGER DEFAULT 0,
                        is_locked INTEGER DEFAULT 0,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """))
                
                # Categories table
                conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS categories (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name TEXT NOT NULL,
                        description TEXT,
                        parent_id INTEGER,
                        color TEXT DEFAULT '#0078D4',
                        is_active INTEGER DEFAULT 1,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (parent_id) REFERENCES categories(id)
                    )
                """))
                
                # Documents table
                conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS documents (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        title TEXT NOT NULL,
                        description TEXT,
                        file_name TEXT NOT NULL,
                        file_path TEXT NOT NULL,
                        file_size INTEGER NOT NULL,
                        file_type TEXT NOT NULL,
                        mime_type TEXT,
                        category_id INTEGER,
                        created_by INTEGER NOT NULL,
                        document_date DATE,
                        tags TEXT,
                        is_encrypted INTEGER DEFAULT 0,
                        thumbnail_path TEXT,
                        full_text_content TEXT,
                        is_active INTEGER DEFAULT 1,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (category_id) REFERENCES categories(id),
                        FOREIGN KEY (created_by) REFERENCES users(id)
                    )
                """))
                
                # Audit log table
                conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS audit_log (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER NOT NULL,
                        action TEXT NOT NULL,
                        table_name TEXT,
                        record_id INTEGER,
                        old_values TEXT,
                        new_values TEXT,
                        ip_address TEXT,
                        user_agent TEXT,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users(id)
                    )
                """))
                
                # Create indexes for better performance
                conn.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_documents_title ON documents(title)
                """))
                
                conn.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_documents_created_at ON documents(created_at)
                """))
                
                conn.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_documents_category_id ON documents(category_id)
                """))
                
                conn.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_audit_log_user_id ON audit_log(user_id)
                """))
                
                conn.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_audit_log_created_at ON audit_log(created_at)
                """))
                
                conn.commit()
                
            self.logger.info("تم إنشاء مخطط قاعدة البيانات SQLite بنجاح")
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء مخطط قاعدة البيانات: {e}")
            raise
    
    def get_session(self) -> Session:
        """
        Get database session
        
        Returns:
            SQLAlchemy session
        """
        if not self.SessionLocal:
            raise RuntimeError("قاعدة البيانات غير مهيئة")
        
        return self.SessionLocal()
    
    def test_connection(self) -> bool:
        """
        Test database connection
        
        Returns:
            True if connection is successful
        """
        try:
            with self.engine.connect() as conn:
                result = conn.execute(text("SELECT 1"))
                result.fetchone()
            return True
        except Exception as e:
            self.logger.error(f"خطأ في اختبار الاتصال: {e}")
            return False
    
    def close(self):
        """إغلاق اتصال قاعدة البيانات"""
        if self.engine:
            self.engine.dispose()
            self.logger.info("تم إغلاق اتصال قاعدة البيانات SQLite")
    
    def get_database_info(self) -> dict:
        """Get database information"""
        return {
            'type': 'SQLite',
            'path': str(self.db_path),
            'size': self.db_path.stat().st_size if self.db_path.exists() else 0,
            'exists': self.db_path.exists()
        }
