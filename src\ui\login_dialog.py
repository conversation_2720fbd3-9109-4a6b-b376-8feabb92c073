"""
Login Dialog for Electronic Archive System
نافذة تسجيل الدخول لنظام الأرشفة الإلكترونية

Handles user authentication and login
"""

from typing import Optional
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLabel, QLineEdit, QPushButton, QCheckBox,
    QMessageBox, QGroupBox, QProgressBar, QFrame
)
from PySide6.QtCore import Qt, QTimer, Signal, QThread
from PySide6.QtGui import QFont, QPixmap, QIcon

from ..core.database import DatabaseManager
from ..models import User


class LoginWorker(QThread):
    """
    Worker thread for login authentication
    خيط عمل للمصادقة
    """
    login_result = Signal(bool, object)  # success, user_or_error
    
    def __init__(self, db_manager: DatabaseManager, username: str, password: str):
        super().__init__()
        self.db_manager = db_manager
        self.username = username
        self.password = password
    
    def run(self):
        """تشغيل عملية المصادقة"""
        try:
            with self.db_manager.get_session() as session:
                user = User.authenticate(session, self.username, self.password)
                if user:
                    self.login_result.emit(True, user)
                else:
                    self.login_result.emit(False, "اسم المستخدم أو كلمة المرور غير صحيحة")
        except Exception as e:
            self.login_result.emit(False, str(e))


class LoginDialog(QDialog):
    """
    Login dialog for user authentication
    نافذة تسجيل الدخول للمصادقة
    """
    
    login_successful = Signal(object)  # User object
    
    def __init__(self, db_manager: DatabaseManager, parent=None):
        """
        Initialize login dialog
        
        Args:
            db_manager: Database manager
            parent: Parent widget
        """
        super().__init__(parent)
        
        self.db_manager = db_manager
        self.current_user: Optional[User] = None
        self.login_worker: Optional[LoginWorker] = None
        
        self.setup_ui()
        self.setup_connections()
        
        # Set dialog properties
        self.setWindowTitle("تسجيل الدخول - نظام الأرشفة الإلكترونية")
        self.setModal(True)
        self.setFixedSize(400, 300)
        self.center_on_screen()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        
        # Logo/Title section
        title_frame = QFrame()
        title_layout = QVBoxLayout(title_frame)
        
        title_label = QLabel("نظام الأرشفة الإلكترونية")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_layout.addWidget(title_label)
        
        subtitle_label = QLabel("Electronic Archive System")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_font = QFont()
        subtitle_font.setPointSize(10)
        subtitle_label.setFont(subtitle_font)
        subtitle_label.setStyleSheet("color: #666;")
        title_layout.addWidget(subtitle_label)
        
        layout.addWidget(title_frame)
        
        # Login form
        form_group = QGroupBox("تسجيل الدخول")
        form_layout = QFormLayout(form_group)
        form_layout.setLabelAlignment(Qt.AlignRight)
        
        # Username field
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("اسم المستخدم أو البريد الإلكتروني")
        form_layout.addRow("اسم المستخدم:", self.username_input)
        
        # Password field
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setPlaceholderText("كلمة المرور")
        form_layout.addRow("كلمة المرور:", self.password_input)
        
        # Remember me checkbox
        self.remember_checkbox = QCheckBox("تذكرني")
        form_layout.addRow("", self.remember_checkbox)
        
        layout.addWidget(form_group)
        
        # Progress bar (hidden by default)
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # Buttons
        buttons_layout = QHBoxLayout()
        
        self.login_button = QPushButton("تسجيل الدخول")
        self.login_button.setDefault(True)
        self.login_button.setMinimumHeight(35)
        
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setMinimumHeight(35)
        
        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addWidget(self.login_button)
        
        layout.addLayout(buttons_layout)
        
        # Status label
        self.status_label = QLabel("")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("color: red;")
        layout.addWidget(self.status_label)
        
        # Add stretch to push everything up
        layout.addStretch()
    
    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        self.login_button.clicked.connect(self.attempt_login)
        self.cancel_button.clicked.connect(self.reject)
        
        # Enter key in password field triggers login
        self.password_input.returnPressed.connect(self.attempt_login)
        self.username_input.returnPressed.connect(self.password_input.setFocus)
    
    def center_on_screen(self):
        """توسيط النافذة على الشاشة"""
        if self.parent():
            # Center on parent window
            parent_geometry = self.parent().frameGeometry()
            dialog_geometry = self.frameGeometry()
            center_point = parent_geometry.center()
            dialog_geometry.moveCenter(center_point)
            self.move(dialog_geometry.topLeft())
        else:
            # Center on screen
            screen = self.screen().availableGeometry()
            window = self.frameGeometry()
            window.moveCenter(screen.center())
            self.move(window.topLeft())
    
    def attempt_login(self):
        """محاولة تسجيل الدخول"""
        username = self.username_input.text().strip()
        password = self.password_input.text()
        
        # Validate input
        if not username:
            self.show_error("يرجى إدخال اسم المستخدم")
            self.username_input.setFocus()
            return
        
        if not password:
            self.show_error("يرجى إدخال كلمة المرور")
            self.password_input.setFocus()
            return
        
        # Disable UI during login
        self.set_login_state(True)
        
        # Start login worker thread
        self.login_worker = LoginWorker(self.db_manager, username, password)
        self.login_worker.login_result.connect(self.on_login_result)
        self.login_worker.start()
    
    def on_login_result(self, success: bool, result):
        """
        Handle login result
        
        Args:
            success: Whether login was successful
            result: User object if successful, error message if not
        """
        self.set_login_state(False)
        
        if success:
            self.current_user = result
            self.show_success("تم تسجيل الدخول بنجاح")
            
            # Emit signal and close dialog
            QTimer.singleShot(500, self.accept_login)
        else:
            self.show_error(f"فشل تسجيل الدخول: {result}")
            self.password_input.clear()
            self.password_input.setFocus()
    
    def accept_login(self):
        """قبول تسجيل الدخول وإغلاق النافذة"""
        self.login_successful.emit(self.current_user)
        self.accept()
    
    def set_login_state(self, logging_in: bool):
        """
        Set UI state during login process
        
        Args:
            logging_in: Whether login is in progress
        """
        self.username_input.setEnabled(not logging_in)
        self.password_input.setEnabled(not logging_in)
        self.remember_checkbox.setEnabled(not logging_in)
        self.login_button.setEnabled(not logging_in)
        self.cancel_button.setEnabled(not logging_in)
        
        if logging_in:
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # Indeterminate progress
            self.status_label.setText("جاري تسجيل الدخول...")
            self.status_label.setStyleSheet("color: blue;")
        else:
            self.progress_bar.setVisible(False)
    
    def show_error(self, message: str):
        """
        Show error message
        
        Args:
            message: Error message
        """
        self.status_label.setText(message)
        self.status_label.setStyleSheet("color: red;")
        
        # Clear message after 5 seconds
        QTimer.singleShot(5000, lambda: self.status_label.setText(""))
    
    def show_success(self, message: str):
        """
        Show success message
        
        Args:
            message: Success message
        """
        self.status_label.setText(message)
        self.status_label.setStyleSheet("color: green;")
    
    def keyPressEvent(self, event):
        """Handle key press events"""
        if event.key() == Qt.Key_Escape:
            self.reject()
        else:
            super().keyPressEvent(event)
    
    def closeEvent(self, event):
        """Handle close event"""
        if self.login_worker and self.login_worker.isRunning():
            # Cancel login if in progress
            self.login_worker.terminate()
            self.login_worker.wait()
        
        event.accept()
    
    @staticmethod
    def get_user_login(db_manager: DatabaseManager, parent=None) -> Optional[User]:
        """
        Static method to show login dialog and return authenticated user
        
        Args:
            db_manager: Database manager
            parent: Parent widget
            
        Returns:
            Authenticated user or None if cancelled
        """
        dialog = LoginDialog(db_manager, parent)
        
        if dialog.exec() == QDialog.Accepted:
            return dialog.current_user
        
        return None
