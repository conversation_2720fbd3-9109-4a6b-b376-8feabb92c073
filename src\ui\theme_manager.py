"""
Theme Manager for Electronic Archive System
مدير المظاهر لنظام الأرشفة الإلكترونية

Handles light/dark themes and high-DPI support
"""

from enum import Enum
from typing import Dict, Any
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QSettings, Signal, QObject
from PySide6.QtGui import QPalette, QColor


class ThemeType(Enum):
    """أنواع المظاهر"""
    LIGHT = "light"
    DARK = "dark"
    AUTO = "auto"  # Follow system theme


class ThemeManager(QObject):
    """
    Theme manager for handling application themes
    مدير المظاهر للتطبيق
    """
    
    theme_changed = Signal(str)  # Emitted when theme changes
    
    def __init__(self):
        super().__init__()
        self.settings = QSettings()
        self.current_theme = ThemeType.LIGHT
        self._load_theme_preference()
    
    def _load_theme_preference(self):
        """تحميل تفضيلات المظهر المحفوظة"""
        saved_theme = self.settings.value("theme", ThemeType.LIGHT.value)
        try:
            self.current_theme = ThemeType(saved_theme)
        except ValueError:
            self.current_theme = ThemeType.LIGHT
    
    def set_theme(self, theme: ThemeType):
        """
        Set application theme
        
        Args:
            theme: Theme type to apply
        """
        if theme != self.current_theme:
            self.current_theme = theme
            self.settings.setValue("theme", theme.value)
            self._apply_theme()
            self.theme_changed.emit(theme.value)
    
    def get_current_theme(self) -> ThemeType:
        """Get current theme"""
        return self.current_theme
    
    def toggle_theme(self):
        """Toggle between light and dark themes"""
        if self.current_theme == ThemeType.LIGHT:
            self.set_theme(ThemeType.DARK)
        else:
            self.set_theme(ThemeType.LIGHT)
    
    def _apply_theme(self):
        """Apply the current theme to the application"""
        app = QApplication.instance()
        if not app:
            return
        
        if self.current_theme == ThemeType.DARK:
            self._apply_dark_theme(app)
        else:
            self._apply_light_theme(app)
    
    def _apply_light_theme(self, app: QApplication):
        """Apply light theme"""
        # Reset to default palette
        app.setPalette(app.style().standardPalette())
        
        # Custom light theme stylesheet
        stylesheet = """
        QMainWindow {
            background-color: #f5f5f5;
            color: #333333;
        }
        
        QMenuBar {
            background-color: #ffffff;
            color: #333333;
            border-bottom: 1px solid #e0e0e0;
        }
        
        QMenuBar::item {
            background-color: transparent;
            padding: 4px 8px;
        }
        
        QMenuBar::item:selected {
            background-color: #e3f2fd;
        }
        
        QToolBar {
            background-color: #ffffff;
            border: 1px solid #e0e0e0;
            spacing: 2px;
        }
        
        QStatusBar {
            background-color: #ffffff;
            color: #666666;
            border-top: 1px solid #e0e0e0;
        }
        
        QTreeWidget, QListWidget, QTableWidget {
            background-color: #ffffff;
            alternate-background-color: #f8f9fa;
            border: 1px solid #e0e0e0;
            selection-background-color: #2196f3;
            selection-color: white;
        }
        
        QLineEdit, QTextEdit, QComboBox {
            background-color: #ffffff;
            border: 1px solid #cccccc;
            border-radius: 4px;
            padding: 4px;
        }
        
        QLineEdit:focus, QTextEdit:focus, QComboBox:focus {
            border-color: #2196f3;
        }
        
        QPushButton {
            background-color: #2196f3;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 6px 12px;
            font-weight: bold;
        }
        
        QPushButton:hover {
            background-color: #1976d2;
        }
        
        QPushButton:pressed {
            background-color: #1565c0;
        }
        
        QPushButton:disabled {
            background-color: #cccccc;
            color: #666666;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 1px solid #cccccc;
            border-radius: 4px;
            margin-top: 8px;
            padding-top: 8px;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 8px;
            padding: 0 4px 0 4px;
        }
        
        QTabWidget::pane {
            border: 1px solid #cccccc;
            background-color: #ffffff;
        }
        
        QTabBar::tab {
            background-color: #f0f0f0;
            border: 1px solid #cccccc;
            padding: 6px 12px;
            margin-right: 2px;
        }
        
        QTabBar::tab:selected {
            background-color: #ffffff;
            border-bottom-color: #ffffff;
        }
        
        QScrollBar:vertical {
            background-color: #f0f0f0;
            width: 12px;
            border-radius: 6px;
        }
        
        QScrollBar::handle:vertical {
            background-color: #cccccc;
            border-radius: 6px;
            min-height: 20px;
        }
        
        QScrollBar::handle:vertical:hover {
            background-color: #999999;
        }
        """
        
        app.setStyleSheet(stylesheet)
    
    def _apply_dark_theme(self, app: QApplication):
        """Apply dark theme"""
        # Create dark palette
        dark_palette = QPalette()
        
        # Window colors
        dark_palette.setColor(QPalette.Window, QColor(53, 53, 53))
        dark_palette.setColor(QPalette.WindowText, QColor(255, 255, 255))
        
        # Base colors (for input fields)
        dark_palette.setColor(QPalette.Base, QColor(25, 25, 25))
        dark_palette.setColor(QPalette.AlternateBase, QColor(53, 53, 53))
        
        # Text colors
        dark_palette.setColor(QPalette.Text, QColor(255, 255, 255))
        dark_palette.setColor(QPalette.BrightText, QColor(255, 0, 0))
        
        # Button colors
        dark_palette.setColor(QPalette.Button, QColor(53, 53, 53))
        dark_palette.setColor(QPalette.ButtonText, QColor(255, 255, 255))
        
        # Highlight colors
        dark_palette.setColor(QPalette.Highlight, QColor(42, 130, 218))
        dark_palette.setColor(QPalette.HighlightedText, QColor(0, 0, 0))
        
        app.setPalette(dark_palette)
        
        # Custom dark theme stylesheet
        stylesheet = """
        QMainWindow {
            background-color: #353535;
            color: #ffffff;
        }
        
        QMenuBar {
            background-color: #2b2b2b;
            color: #ffffff;
            border-bottom: 1px solid #555555;
        }
        
        QMenuBar::item {
            background-color: transparent;
            padding: 4px 8px;
        }
        
        QMenuBar::item:selected {
            background-color: #404040;
        }
        
        QToolBar {
            background-color: #2b2b2b;
            border: 1px solid #555555;
            spacing: 2px;
        }
        
        QStatusBar {
            background-color: #2b2b2b;
            color: #cccccc;
            border-top: 1px solid #555555;
        }
        
        QTreeWidget, QListWidget, QTableWidget {
            background-color: #1e1e1e;
            alternate-background-color: #2a2a2a;
            border: 1px solid #555555;
            selection-background-color: #2a82da;
            selection-color: white;
        }
        
        QLineEdit, QTextEdit, QComboBox {
            background-color: #1e1e1e;
            border: 1px solid #555555;
            border-radius: 4px;
            padding: 4px;
            color: #ffffff;
        }
        
        QLineEdit:focus, QTextEdit:focus, QComboBox:focus {
            border-color: #2a82da;
        }
        
        QPushButton {
            background-color: #2a82da;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 6px 12px;
            font-weight: bold;
        }
        
        QPushButton:hover {
            background-color: #1976d2;
        }
        
        QPushButton:pressed {
            background-color: #1565c0;
        }
        
        QPushButton:disabled {
            background-color: #555555;
            color: #888888;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 1px solid #555555;
            border-radius: 4px;
            margin-top: 8px;
            padding-top: 8px;
            color: #ffffff;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 8px;
            padding: 0 4px 0 4px;
        }
        
        QTabWidget::pane {
            border: 1px solid #555555;
            background-color: #353535;
        }
        
        QTabBar::tab {
            background-color: #2b2b2b;
            border: 1px solid #555555;
            padding: 6px 12px;
            margin-right: 2px;
            color: #ffffff;
        }
        
        QTabBar::tab:selected {
            background-color: #353535;
            border-bottom-color: #353535;
        }
        
        QScrollBar:vertical {
            background-color: #2b2b2b;
            width: 12px;
            border-radius: 6px;
        }
        
        QScrollBar::handle:vertical {
            background-color: #555555;
            border-radius: 6px;
            min-height: 20px;
        }
        
        QScrollBar::handle:vertical:hover {
            background-color: #777777;
        }
        """
        
        app.setStyleSheet(stylesheet)
    
    def setup_high_dpi_support(self):
        """Setup high-DPI support"""
        app = QApplication.instance()
        if app:
            # Enable high-DPI scaling
            app.setAttribute(app.AA_EnableHighDpiScaling, True)
            app.setAttribute(app.AA_UseHighDpiPixmaps, True)
    
    def get_theme_icon(self, icon_name: str) -> str:
        """
        Get theme-appropriate icon path
        
        Args:
            icon_name: Base icon name
            
        Returns:
            Path to theme-appropriate icon
        """
        theme_suffix = "dark" if self.current_theme == ThemeType.DARK else "light"
        return f"resources/icons/{icon_name}_{theme_suffix}.png"


# Global theme manager instance
theme_manager = ThemeManager()
