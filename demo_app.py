#!/usr/bin/env python3
"""
Demo Electronic Archive System
نظام الأرشفة الإلكترونية التجريبي

A simple demo version to showcase the UI and basic functionality
"""

import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QLineEdit, QTextEdit, QListWidget, QSplitter,
    QGroupBox, QFormLayout, QMessageBox, QTabWidget, QTreeWidget,
    QTreeWidgetItem, QTableWidget, QTableWidgetItem, QHeaderView
)
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont, QIcon


class DemoArchiveWindow(QMainWindow):
    """نافذة تجريبية لنظام الأرشفة"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.load_demo_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("نظام الأرشفة الإلكترونية - Electronic Archive System (Demo)")
        self.setGeometry(100, 100, 1200, 800)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QHBoxLayout(central_widget)
        
        # Create splitter
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # Left panel
        self.setup_left_panel(splitter)
        
        # Right panel
        self.setup_right_panel(splitter)
        
        # Set splitter proportions
        splitter.setSizes([300, 900])
        
        # Setup menu and status bar
        self.setup_menu_bar()
        self.setup_status_bar()
    
    def setup_left_panel(self, parent):
        """إعداد اللوحة اليسرى"""
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        
        # Search section
        search_group = QGroupBox("البحث - Search")
        search_layout = QVBoxLayout(search_group)
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("البحث في الوثائق... Search documents...")
        search_layout.addWidget(self.search_input)
        
        search_button = QPushButton("بحث - Search")
        search_button.clicked.connect(self.perform_search)
        search_layout.addWidget(search_button)
        
        left_layout.addWidget(search_group)
        
        # Categories section
        categories_group = QGroupBox("الفئات - Categories")
        categories_layout = QVBoxLayout(categories_group)
        
        self.categories_tree = QTreeWidget()
        self.categories_tree.setHeaderLabel("الفئات - Categories")
        self.categories_tree.itemClicked.connect(self.on_category_selected)
        categories_layout.addWidget(self.categories_tree)
        
        left_layout.addWidget(categories_group)
        
        # Quick actions
        actions_group = QGroupBox("إجراءات سريعة - Quick Actions")
        actions_layout = QVBoxLayout(actions_group)
        
        add_doc_btn = QPushButton("إضافة وثيقة - Add Document")
        add_doc_btn.clicked.connect(self.add_document)
        actions_layout.addWidget(add_doc_btn)
        
        recent_btn = QPushButton("الوثائق الحديثة - Recent")
        recent_btn.clicked.connect(self.show_recent)
        actions_layout.addWidget(recent_btn)
        
        left_layout.addWidget(actions_group)
        
        parent.addWidget(left_widget)
    
    def setup_right_panel(self, parent):
        """إعداد اللوحة اليمنى"""
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        
        # Documents table
        self.documents_table = QTableWidget()
        self.setup_documents_table()
        right_layout.addWidget(self.documents_table)
        
        # Document details
        details_group = QGroupBox("تفاصيل الوثيقة - Document Details")
        details_layout = QFormLayout(details_group)
        
        self.title_label = QLabel("-")
        self.type_label = QLabel("-")
        self.size_label = QLabel("-")
        self.date_label = QLabel("-")
        
        details_layout.addRow("العنوان - Title:", self.title_label)
        details_layout.addRow("النوع - Type:", self.type_label)
        details_layout.addRow("الحجم - Size:", self.size_label)
        details_layout.addRow("التاريخ - Date:", self.date_label)
        
        right_layout.addWidget(details_group)
        
        parent.addWidget(right_widget)
    
    def setup_documents_table(self):
        """إعداد جدول الوثائق"""
        headers = ["العنوان - Title", "النوع - Type", "الحجم - Size", "التاريخ - Date"]
        self.documents_table.setColumnCount(len(headers))
        self.documents_table.setHorizontalHeaderLabels(headers)
        
        # Set table properties
        self.documents_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.documents_table.setAlternatingRowColors(True)
        
        # Connect selection
        self.documents_table.itemSelectionChanged.connect(self.on_document_selected)
        
        # Set column widths
        header = self.documents_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
    
    def setup_menu_bar(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu("ملف - File")
        
        new_action = file_menu.addAction("جديد - New")
        new_action.triggered.connect(self.add_document)
        
        file_menu.addSeparator()
        
        exit_action = file_menu.addAction("خروج - Exit")
        exit_action.triggered.connect(self.close)
        
        # Help menu
        help_menu = menubar.addMenu("مساعدة - Help")
        
        about_action = help_menu.addAction("حول - About")
        about_action.triggered.connect(self.show_about)
    
    def setup_status_bar(self):
        """إعداد شريط الحالة"""
        self.status_bar = self.statusBar()
        self.status_bar.showMessage("جاهز - Ready")
    
    def load_demo_data(self):
        """تحميل بيانات تجريبية"""
        # Load categories
        categories_data = [
            ("الوثائق الإدارية - Administrative", [
                "المراسلات - Correspondence",
                "التقارير - Reports"
            ]),
            ("الوثائق المالية - Financial", [
                "الفواتير - Invoices",
                "الإيصالات - Receipts"
            ]),
            ("الوثائق القانونية - Legal", [
                "العقود - Contracts",
                "التراخيص - Licenses"
            ])
        ]
        
        for category_name, subcategories in categories_data:
            category_item = QTreeWidgetItem([category_name])
            self.categories_tree.addTopLevelItem(category_item)
            
            for subcategory in subcategories:
                sub_item = QTreeWidgetItem([subcategory])
                category_item.addChild(sub_item)
        
        self.categories_tree.expandAll()
        
        # Load sample documents
        documents_data = [
            ("تقرير شهري - Monthly Report", "PDF", "2.5 MB", "2025-08-15"),
            ("عقد عمل - Employment Contract", "DOCX", "1.2 MB", "2025-08-10"),
            ("فاتورة كهرباء - Electricity Bill", "PDF", "0.8 MB", "2025-08-05"),
            ("رخصة تجارية - Business License", "PDF", "1.5 MB", "2025-07-30"),
            ("مراسلة داخلية - Internal Memo", "DOCX", "0.5 MB", "2025-07-25"),
        ]
        
        self.documents_table.setRowCount(len(documents_data))
        
        for row, (title, doc_type, size, date) in enumerate(documents_data):
            self.documents_table.setItem(row, 0, QTableWidgetItem(title))
            self.documents_table.setItem(row, 1, QTableWidgetItem(doc_type))
            self.documents_table.setItem(row, 2, QTableWidgetItem(size))
            self.documents_table.setItem(row, 3, QTableWidgetItem(date))
    
    def on_category_selected(self, item):
        """عند اختيار فئة"""
        category_name = item.text(0)
        self.status_bar.showMessage(f"تم اختيار الفئة: {category_name}")
    
    def on_document_selected(self):
        """عند اختيار وثيقة"""
        current_row = self.documents_table.currentRow()
        if current_row >= 0:
            title = self.documents_table.item(current_row, 0).text()
            doc_type = self.documents_table.item(current_row, 1).text()
            size = self.documents_table.item(current_row, 2).text()
            date = self.documents_table.item(current_row, 3).text()
            
            self.title_label.setText(title)
            self.type_label.setText(doc_type)
            self.size_label.setText(size)
            self.date_label.setText(date)
            
            self.status_bar.showMessage(f"تم اختيار الوثيقة: {title}")
    
    def perform_search(self):
        """تنفيذ البحث"""
        query = self.search_input.text()
        if query:
            self.status_bar.showMessage(f"البحث عن: {query}")
            QMessageBox.information(self, "البحث - Search", f"البحث عن: {query}\nهذه ميزة تجريبية")
        else:
            QMessageBox.warning(self, "تحذير - Warning", "يرجى إدخال كلمة البحث")
    
    def add_document(self):
        """إضافة وثيقة جديدة"""
        QMessageBox.information(
            self, 
            "إضافة وثيقة - Add Document", 
            "ميزة إضافة الوثائق ستكون متاحة في النسخة الكاملة\n"
            "Document addition feature will be available in the full version"
        )
    
    def show_recent(self):
        """عرض الوثائق الحديثة"""
        self.status_bar.showMessage("عرض الوثائق الحديثة - Showing recent documents")
    
    def show_about(self):
        """عرض معلومات البرنامج"""
        QMessageBox.about(
            self,
            "حول البرنامج - About",
            """
نظام الأرشفة الإلكترونية (تجريبي)
Electronic Archive System (Demo)

الإصدار: 1.0.0 (Demo)
Version: 1.0.0 (Demo)

نظام شامل لإدارة الوثائق والأرشفة الإلكترونية
A comprehensive document management and electronic archiving system

الميزات:
Features:
• إدارة الوثائق - Document Management
• البحث النصي الكامل - Full-text Search  
• تنظيم هرمي - Hierarchical Organization
• واجهة عربية/إنجليزية - Arabic/English Interface
• أمان متقدم - Advanced Security

© 2025 Archive Solutions
            """
        )


def main():
    """الدالة الرئيسية"""
    print("🚀 بدء تشغيل النظام التجريبي...")
    print("🚀 Starting demo system...")
    
    # Create application
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("Electronic Archive System (Demo)")
    app.setApplicationVersion("1.0.0")
    
    # Create and show window
    window = DemoArchiveWindow()
    window.show()
    
    print("✅ تم تشغيل النظام التجريبي بنجاح!")
    print("✅ Demo system started successfully!")
    
    # Run application
    return app.exec()


if __name__ == "__main__":
    sys.exit(main())
