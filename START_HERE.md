# 🚀 ابدأ من هنا - START HERE

## نظام الأرشفة الإلكترونية - Electronic Archive System

### 🎯 التشغيل السريع - Quick Start

#### الخطوة 1: تثبيت المتطلبات الأساسية
```bash
python -m pip install PySide6 sqlalchemy bcrypt python-dateutil
```

#### الخطوة 2: اختبار النظام
اختر أحد التطبيقات التالية للتشغيل:

---

## 📱 التطبيقات المتاحة - Available Applications

### 1. 🎮 التطبيق التجريبي (مستحسن للبداية)
```bash
python demo_app.py
```
**الميزات:**
- ✅ واجهة مستخدم كاملة
- ✅ بيانات تجريبية
- ✅ لا يحتاج قاعدة بيانات
- ✅ يعمل فوراً بدون إعداد

### 2. 🧪 تطبيق الاختبار
```bash
python test_app.py
```
**الميزات:**
- ✅ اختبار المكتبات والوظائف
- ✅ تشخيص المشاكل
- ✅ واجهة بسيطة

### 3. 🗄️ النظام المبسط (SQLite)
```bash
python main_simple.py
```
**الميزات:**
- ✅ نظام كامل مع قاعدة بيانات SQLite
- ✅ تسجيل دخول: admin / Admin123!
- ✅ إدارة وثائق حقيقية

### 4. 🏢 النظام الكامل (SQL Server)
```bash
# أولاً: إعداد قاعدة البيانات
python setup_database.py

# ثانياً: تشغيل النظام
python main.py
```
**المتطلبات:**
- SQL Server مثبت ومُعد
- pyodbc مثبت

---

## 🔧 حل المشاكل - Troubleshooting

### مشكلة: ModuleNotFoundError
```bash
# الحل: تثبيت المكتبة المفقودة
python -m pip install [اسم المكتبة]

# مثال:
python -m pip install PySide6
```

### مشكلة: خطأ في قاعدة البيانات
```bash
# استخدم النظام المبسط بدلاً من الكامل
python main_simple.py
```

### مشكلة: لا يعمل أي شيء
```bash
# ابدأ بالتطبيق التجريبي
python demo_app.py
```

---

## 📋 ترتيب التشغيل المستحسن

### للمبتدئين:
1. `python demo_app.py` - للتعرف على الواجهة
2. `python test_app.py` - للتأكد من عمل النظام
3. `python main_simple.py` - للتجربة الكاملة

### للمطورين:
1. `python test_app.py` - اختبار النظام
2. `python main_simple.py` - تطوير مع SQLite
3. `python setup_database.py` - إعداد SQL Server
4. `python main.py` - النظام الكامل

---

## 🎯 الميزات حسب التطبيق

| التطبيق | الواجهة | قاعدة البيانات | تسجيل الدخول | إدارة الوثائق |
|---------|---------|---------------|-------------|--------------|
| `demo_app.py` | ✅ كاملة | ❌ تجريبية | ❌ لا | ❌ تجريبية |
| `test_app.py` | ✅ بسيطة | ❌ لا | ❌ لا | ❌ لا |
| `main_simple.py` | ✅ كاملة | ✅ SQLite | ✅ نعم | ✅ نعم |
| `main.py` | ✅ كاملة | ✅ SQL Server | ✅ نعم | ✅ نعم |

---

## 🔐 بيانات تسجيل الدخول

### للأنظمة التي تتطلب تسجيل دخول:
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `Admin123!`

---

## 📁 الملفات المهمة

| الملف | الوصف | متى تستخدمه |
|-------|--------|-------------|
| `demo_app.py` | تطبيق تجريبي | للتعرف على النظام |
| `test_app.py` | تطبيق اختبار | لحل المشاكل |
| `main_simple.py` | نظام مبسط | للاستخدام العادي |
| `main.py` | النظام الكامل | للإنتاج |
| `setup_database.py` | إعداد قاعدة البيانات | مرة واحدة فقط |

---

## 🎉 البدء السريع (30 ثانية)

```bash
# 1. تثبيت المتطلبات
python -m pip install PySide6

# 2. تشغيل التطبيق التجريبي
python demo_app.py
```

**🎊 مبروك! النظام يعمل الآن!**

---

## 📞 المساعدة

إذا واجهت مشاكل:
1. ابدأ بـ `python demo_app.py`
2. راجع ملف `INSTALL.md` للتفاصيل
3. تحقق من ملف `logs/archive.log`

---

## 🌟 الخطوات التالية

بعد تشغيل النظام بنجاح:
1. استكشف الواجهة والميزات
2. أضف وثائق تجريبية
3. جرب البحث والتصفية
4. اقرأ `README.md` للمزيد من التفاصيل

**🚀 استمتع باستخدام نظام الأرشفة الإلكترونية!**
