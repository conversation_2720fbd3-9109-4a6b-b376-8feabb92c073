"""
Logging Configuration for Electronic Archive System
تكوين السجلات لنظام الأرشفة الإلكترونية

Configures application logging with file rotation and different log levels
"""

import logging
import logging.handlers
import os
from pathlib import Path
from datetime import datetime


def setup_logging(
    log_level: str = "INFO",
    log_file: str = "./logs/archive.log",
    max_log_size: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5
):
    """
    Setup application logging configuration
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Path to log file
        max_log_size: Maximum log file size in bytes
        backup_count: Number of backup log files to keep
    """
    
    # Create logs directory if it doesn't exist
    log_path = Path(log_file)
    log_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Configure root logger
    logger = logging.getLogger()
    logger.setLevel(getattr(logging, log_level.upper(), logging.INFO))
    
    # Clear existing handlers
    logger.handlers.clear()
    
    # Create formatter
    formatter = logging.Formatter(
        fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Arabic-friendly formatter for Arabic messages
    arabic_formatter = logging.Formatter(
        fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # File handler with rotation
    file_handler = logging.handlers.RotatingFileHandler(
        filename=log_file,
        maxBytes=max_log_size,
        backupCount=backup_count,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(arabic_formatter)
    logger.addHandler(file_handler)
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # Create separate error log file
    error_log_file = log_path.parent / "error.log"
    error_handler = logging.handlers.RotatingFileHandler(
        filename=str(error_log_file),
        maxBytes=max_log_size,
        backupCount=backup_count,
        encoding='utf-8'
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(arabic_formatter)
    logger.addHandler(error_handler)
    
    # Log startup message
    logger.info("تم تهيئة نظام السجلات")
    logger.info(f"مستوى السجل: {log_level}")
    logger.info(f"ملف السجل: {log_file}")


def get_logger(name: str) -> logging.Logger:
    """
    Get logger instance for a specific module
    
    Args:
        name: Logger name (usually __name__)
        
    Returns:
        Logger instance
    """
    return logging.getLogger(name)


class DatabaseLogHandler(logging.Handler):
    """
    Custom log handler that writes logs to database
    معالج سجلات مخصص يكتب السجلات في قاعدة البيانات
    """
    
    def __init__(self, db_manager):
        """
        Initialize database log handler
        
        Args:
            db_manager: Database manager instance
        """
        super().__init__()
        self.db_manager = db_manager
    
    def emit(self, record):
        """
        Emit log record to database
        
        Args:
            record: Log record
        """
        try:
            # Format the log message
            message = self.format(record)
            
            # Insert into database (implement based on your audit_log table)
            with self.db_manager.get_session() as session:
                # This would be implemented with your audit log model
                pass
                
        except Exception:
            # Don't raise exceptions in log handler
            self.handleError(record)


class AuditLogger:
    """
    Specialized logger for audit trails
    مسجل متخصص لسجلات التدقيق
    """
    
    def __init__(self, db_manager=None):
        """
        Initialize audit logger
        
        Args:
            db_manager: Database manager for storing audit logs
        """
        self.logger = logging.getLogger('audit')
        self.db_manager = db_manager
        
        # Setup audit log file
        audit_log_path = Path('./logs/audit.log')
        audit_log_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Create audit file handler
        audit_handler = logging.handlers.RotatingFileHandler(
            filename=str(audit_log_path),
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=10,
            encoding='utf-8'
        )
        
        audit_formatter = logging.Formatter(
            fmt='%(asctime)s - AUDIT - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        audit_handler.setFormatter(audit_formatter)
        
        self.logger.addHandler(audit_handler)
        self.logger.setLevel(logging.INFO)
    
    def log_user_action(self, user_id: int, action: str, details: str = ""):
        """
        Log user action for audit trail
        
        Args:
            user_id: User ID performing the action
            action: Action performed
            details: Additional details
        """
        message = f"المستخدم {user_id} - {action}"
        if details:
            message += f" - {details}"
        
        self.logger.info(message)
    
    def log_document_action(self, user_id: int, document_id: int, action: str):
        """
        Log document-related action
        
        Args:
            user_id: User ID performing the action
            document_id: Document ID
            action: Action performed
        """
        message = f"المستخدم {user_id} - {action} - الوثيقة {document_id}"
        self.logger.info(message)
    
    def log_system_event(self, event: str, details: str = ""):
        """
        Log system event
        
        Args:
            event: System event
            details: Additional details
        """
        message = f"حدث النظام - {event}"
        if details:
            message += f" - {details}"
        
        self.logger.info(message)
