#!/usr/bin/env python3
"""
Electronic Archive System - Complete Solution
نظام الأرشفة الإلكترونية - الحل المتكامل

A comprehensive document management system with all essential features
"""

import sys
import os
import logging
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PySide6.QtCore import QTimer, Qt, QSettings
from PySide6.QtGui import QPixmap, QFont, QPalette, QColor

# Core imports
from src.core.config import Config
from src.core.database_sqlite import DatabaseManagerSQLite
from src.core.logger import setup_logging

# UI imports
from src.ui.theme_manager import theme_manager, ThemeType


class ArchiveSystemApp:
    """
    Complete Electronic Archive System Application
    تطبيق نظام الأرشفة الإلكترونية المتكامل
    """
    
    def __init__(self):
        """Initialize the application"""
        self.app = None
        self.config = None
        self.db_manager = None
        self.main_window = None
        self.services = {}
        
        # Setup logging
        setup_logging()
        self.logger = logging.getLogger(__name__)
    
    def initialize(self) -> bool:
        """
        Initialize the complete application
        
        Returns:
            True if successful
        """
        try:
            # Create QApplication
            self.app = QApplication(sys.argv)
            
            # Set application properties
            self.app.setApplicationName("Electronic Archive System")
            self.app.setApplicationVersion("1.0.0")
            self.app.setOrganizationName("Archive Solutions")
            self.app.setOrganizationDomain("archive-solutions.com")
            
            # Setup high-DPI support
            self.app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
            self.app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
            
            # Show splash screen
            splash = self._create_splash_screen()
            splash.show()
            self.app.processEvents()
            
            # Load configuration
            splash.showMessage("تحميل التكوين... Loading configuration...", 
                             Qt.AlignBottom | Qt.AlignCenter, Qt.white)
            self.app.processEvents()
            
            self.config = Config()
            self.config.ensure_directories()
            
            # Initialize database
            splash.showMessage("تهيئة قاعدة البيانات... Initializing database...", 
                             Qt.AlignBottom | Qt.AlignCenter, Qt.white)
            self.app.processEvents()
            
            self.db_manager = DatabaseManagerSQLite(self.config)
            if not self.db_manager.initialize():
                splash.close()
                QMessageBox.critical(None, "خطأ", "فشل في تهيئة قاعدة البيانات")
                return False
            
            # Initialize services
            splash.showMessage("تهيئة الخدمات... Initializing services...", 
                             Qt.AlignBottom | Qt.AlignCenter, Qt.white)
            self.app.processEvents()
            
            self._initialize_services()
            
            # Setup theme
            splash.showMessage("تطبيق المظهر... Applying theme...", 
                             Qt.AlignBottom | Qt.AlignCenter, Qt.white)
            self.app.processEvents()
            
            self._setup_theme()
            
            # Create initial data
            splash.showMessage("إعداد البيانات الأولية... Setting up initial data...", 
                             Qt.AlignBottom | Qt.AlignCenter, Qt.white)
            self.app.processEvents()
            
            self._create_initial_data()
            
            # Close splash screen
            QTimer.singleShot(1000, splash.close)
            
            self.logger.info("تم تهيئة النظام بنجاح")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في تهيئة التطبيق: {e}")
            if 'splash' in locals():
                splash.close()
            QMessageBox.critical(None, "خطأ", f"فشل في تهيئة التطبيق: {e}")
            return False
    
    def _create_splash_screen(self) -> QSplashScreen:
        """Create application splash screen"""
        # Create splash screen pixmap
        pixmap = QPixmap(500, 350)
        pixmap.fill(QColor(33, 150, 243))  # Material Blue
        
        splash = QSplashScreen(pixmap)
        splash.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.SplashScreen)
        
        # Set font for splash text
        font = QFont("Arial", 12, QFont.Bold)
        splash.setFont(font)
        
        return splash
    
    def _initialize_services(self):
        """Initialize all system services"""
        try:
            # Import services dynamically to handle missing dependencies
            services_to_load = [
                ('search_fts5', 'SearchServiceFTS5'),
                ('preview_service', 'PreviewService'),
                ('scanning_service', 'ScanningService'),
                ('export_service', 'ExportService'),
                ('trash_service', 'TrashService'),
                ('document_service', 'DocumentService'),
                ('file_service', 'FileService')
            ]
            
            for module_name, class_name in services_to_load:
                try:
                    module = __import__(f'src.services.{module_name}', fromlist=[class_name])
                    service_class = getattr(module, class_name)
                    
                    # Initialize service based on its requirements
                    if class_name in ['SearchServiceFTS5', 'TrashService', 'DocumentService']:
                        service = service_class(self.db_manager)
                    else:
                        service = service_class(self.config)
                    
                    self.services[module_name.replace('_service', '')] = service
                    self.logger.info(f"تم تحميل خدمة: {class_name}")
                    
                except ImportError as e:
                    self.logger.warning(f"لم يتم تحميل خدمة {class_name}: {e}")
                except Exception as e:
                    self.logger.error(f"خطأ في تحميل خدمة {class_name}: {e}")
            
            self.logger.info(f"تم تحميل {len(self.services)} خدمة")
            
        except Exception as e:
            self.logger.error(f"خطأ في تهيئة الخدمات: {e}")
    
    def _setup_theme(self):
        """Setup application theme"""
        try:
            # Load saved theme preference
            settings = QSettings()
            saved_theme = settings.value("theme", "light")
            
            if saved_theme == "dark":
                theme_manager.set_theme(ThemeType.DARK)
            else:
                theme_manager.set_theme(ThemeType.LIGHT)
            
            self.logger.info(f"تم تطبيق المظهر: {saved_theme}")
            
        except Exception as e:
            self.logger.error(f"خطأ في إعداد المظهر: {e}")
    
    def _create_initial_data(self):
        """Create initial data if needed"""
        try:
            # Import models
            from src.models import User, Category, UserRole
            
            with self.db_manager.get_session() as session:
                # Create admin user if not exists
                admin_user = session.query(User).filter(User.username == "admin").first()
                if not admin_user:
                    admin_user = User(
                        username="admin",
                        email="<EMAIL>",
                        full_name="مدير النظام",
                        role=UserRole.ADMIN
                    )
                    admin_user.set_password("Admin123!")
                    session.add(admin_user)
                    session.commit()
                    self.logger.info("تم إنشاء مستخدم المدير")
                
                # Create sample categories if not exist
                categories_count = session.query(Category).count()
                if categories_count == 0:
                    categories = [
                        Category(name="الوثائق الإدارية", description="الوثائق والمراسلات الإدارية", color="#2196F3"),
                        Category(name="الوثائق المالية", description="الفواتير والمستندات المالية", color="#4CAF50"),
                        Category(name="الوثائق القانونية", description="العقود والوثائق القانونية", color="#F44336"),
                        Category(name="الموارد البشرية", description="وثائق الموظفين والموارد البشرية", color="#FF9800"),
                        Category(name="التقارير", description="التقارير الدورية والإحصائيات", color="#9C27B0"),
                        Category(name="الصور والوسائط", description="الصور والملفات الوسائطية", color="#E91E63"),
                    ]
                    
                    for category in categories:
                        session.add(category)
                    
                    session.commit()
                    self.logger.info("تم إنشاء الفئات الأولية")
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء البيانات الأولية: {e}")
    
    def show_main_window(self) -> bool:
        """Show the main application window"""
        try:
            # Import main window
            from src.ui.complete_main_window import CompleteMainWindow
            
            # Show login dialog first
            from src.ui.login_dialog import LoginDialog
            user = LoginDialog.get_user_login(self.db_manager)
            
            if not user:
                self.logger.info("تم إلغاء تسجيل الدخول")
                return False
            
            # Create main window
            self.main_window = CompleteMainWindow(
                config=self.config,
                db_manager=self.db_manager,
                services=self.services
            )
            
            # Set current user
            self.main_window.set_current_user(user)
            
            # Show window
            self.main_window.show()
            
            self.logger.info(f"تم عرض النافذة الرئيسية للمستخدم: {user.username}")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في عرض النافذة الرئيسية: {e}")
            QMessageBox.critical(None, "خطأ", f"فشل في عرض النافذة الرئيسية: {e}")
            return False
    
    def run(self) -> int:
        """
        Run the application
        
        Returns:
            Exit code
        """
        try:
            # Initialize application
            if not self.initialize():
                return 1
            
            # Show main window
            if not self.show_main_window():
                return 0  # User cancelled login
            
            # Setup periodic tasks
            self._setup_periodic_tasks()
            
            # Run application event loop
            return self.app.exec()
            
        except Exception as e:
            self.logger.error(f"خطأ في تشغيل التطبيق: {e}")
            return 1
    
    def _setup_periodic_tasks(self):
        """Setup periodic maintenance tasks"""
        try:
            # Auto-cleanup timer (runs every hour)
            cleanup_timer = QTimer()
            cleanup_timer.timeout.connect(self._perform_maintenance)
            cleanup_timer.start(3600000)  # 1 hour
            
            self.logger.info("تم إعداد المهام الدورية")
            
        except Exception as e:
            self.logger.error(f"خطأ في إعداد المهام الدورية: {e}")
    
    def _perform_maintenance(self):
        """Perform periodic maintenance tasks"""
        try:
            # Auto-cleanup trash
            if 'trash' in self.services:
                deleted_count = self.services['trash'].auto_cleanup_trash()
                if deleted_count > 0:
                    self.logger.info(f"تم تنظيف {deleted_count} وثيقة من سلة المهملات")
            
            # Cleanup scanning temp files
            if 'scanning' in self.services:
                self.services['scanning'].cleanup_temp_files()
            
            # Optimize search index
            if 'search_fts5' in self.services:
                self.services['search_fts5'].optimize_fts_index()
            
        except Exception as e:
            self.logger.error(f"خطأ في المهام الدورية: {e}")


def main():
    """Main entry point"""
    print("=" * 60)
    print("نظام الأرشفة الإلكترونية المتكامل")
    print("Complete Electronic Archive System")
    print("=" * 60)
    print("الميزات الأساسية:")
    print("Core Features:")
    print("• إدارة الوثائق الشاملة - Complete Document Management")
    print("• بحث نصي كامل FTS5 - Full-text Search with FTS5")
    print("• معاينة وصور مصغرة - Previews & Thumbnails")
    print("• مسح ضوئي مدمج - Integrated Scanning")
    print("• تصدير وطباعة - Export & Print")
    print("• سمات فاتحة وداكنة - Light & Dark Themes")
    print("=" * 60)
    
    # Create and run application
    app = ArchiveSystemApp()
    
    try:
        exit_code = app.run()
        print(f"\nتم إنهاء التطبيق بالكود: {exit_code}")
        return exit_code
        
    except KeyboardInterrupt:
        print("\nتم إيقاف التطبيق بواسطة المستخدم")
        return 0
    except Exception as e:
        print(f"\nخطأ غير متوقع: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
