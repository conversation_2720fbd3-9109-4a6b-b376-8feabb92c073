"""
Basic tests for Electronic Archive System
اختبارات أساسية لنظام الأرشفة الإلكترونية

Basic functionality tests
"""

import pytest
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.config import Config
from src.core.database import DatabaseManager
from src.models import User, Category, Document, UserRole
from src.utils.helpers import (
    format_file_size, 
    sanitize_filename, 
    validate_email,
    validate_password_strength
)


class TestConfig:
    """Test configuration management"""
    
    def test_config_creation(self):
        """Test config creation"""
        config = Config()
        assert config is not None
        
        # Test default values
        assert config.get('APPLICATION', 'app_name') == 'Electronic Archive System'
        assert config.getint('APPLICATION', 'window_width') == 1200
        assert config.getboolean('SECURITY', 'encryption_enabled') == True
    
    def test_config_storage_paths(self):
        """Test storage paths configuration"""
        config = Config()
        paths = config.storage_paths
        
        assert 'documents' in paths
        assert 'thumbnails' in paths
        assert 'temp' in paths
        assert 'backup' in paths
        
        # All paths should be Path objects
        for path in paths.values():
            assert isinstance(path, Path)


class TestHelpers:
    """Test utility helper functions"""
    
    def test_format_file_size(self):
        """Test file size formatting"""
        assert format_file_size(0) == "0 بايت"
        assert format_file_size(1024) == "1.0 كيلوبايت"
        assert format_file_size(1024 * 1024) == "1.0 ميجابايت"
        assert format_file_size(1024 * 1024 * 1024) == "1.0 جيجابايت"
    
    def test_sanitize_filename(self):
        """Test filename sanitization"""
        assert sanitize_filename("test<file>.txt") == "test_file_.txt"
        assert sanitize_filename("file/with\\path.doc") == "file_with_path.doc"
        assert sanitize_filename("  .hidden  ") == "hidden"
    
    def test_validate_email(self):
        """Test email validation"""
        assert validate_email("<EMAIL>") == True
        assert validate_email("<EMAIL>") == True
        assert validate_email("invalid.email") == False
        assert validate_email("@domain.com") == False
        assert validate_email("user@") == False
    
    def test_validate_password_strength(self):
        """Test password strength validation"""
        # Weak password
        result = validate_password_strength("123")
        assert result['is_valid'] == False
        assert len(result['errors']) > 0
        
        # Strong password
        result = validate_password_strength("StrongPass123!")
        assert result['is_valid'] == True
        assert len(result['errors']) == 0
        assert result['score'] == 5


class TestModels:
    """Test database models"""
    
    def test_user_model(self):
        """Test User model"""
        # Test user creation (without database)
        user = User(
            username="testuser",
            email="<EMAIL>",
            full_name="Test User",
            role=UserRole.USER
        )
        
        assert user.username == "testuser"
        assert user.email == "<EMAIL>"
        assert user.role == UserRole.USER
        
        # Test password hashing
        user.set_password("testpassword")
        assert user.password_hash is not None
        assert user.check_password("testpassword") == True
        assert user.check_password("wrongpassword") == False
    
    def test_category_model(self):
        """Test Category model"""
        # Test category creation
        category = Category(
            name="Test Category",
            description="Test description",
            color="#FF0000"
        )
        
        assert category.name == "Test Category"
        assert category.description == "Test description"
        assert category.color == "#FF0000"
        assert category.level == 0  # Root category
    
    def test_document_model(self):
        """Test Document model"""
        # Test document creation
        document = Document(
            title="Test Document",
            description="Test description",
            file_name="test.pdf",
            file_path="/path/to/test.pdf",
            file_size=1024,
            file_type="pdf",
            mime_type="application/pdf",
            created_by=1
        )
        
        assert document.title == "Test Document"
        assert document.file_name == "test.pdf"
        assert document.file_extension == "pdf"
        assert document.file_size_mb == 0.0  # 1024 bytes = 0.0 MB (rounded)
        assert document.is_pdf == True
        assert document.is_image == False


class TestDatabaseConnection:
    """Test database connection (requires database setup)"""
    
    @pytest.fixture
    def config(self):
        """Create test configuration"""
        return Config()
    
    @pytest.fixture
    def db_manager(self, config):
        """Create database manager"""
        return DatabaseManager(config)
    
    def test_database_manager_creation(self, db_manager):
        """Test database manager creation"""
        assert db_manager is not None
        assert db_manager.config is not None
    
    @pytest.mark.integration
    def test_database_connection(self, db_manager):
        """Test database connection (integration test)"""
        # This test requires actual database setup
        # Skip if database is not available
        try:
            success = db_manager.initialize()
            if success:
                assert db_manager.test_connection() == True
            else:
                pytest.skip("Database not available for testing")
        except Exception:
            pytest.skip("Database connection failed")


# Test fixtures and utilities
@pytest.fixture
def sample_user_data():
    """Sample user data for testing"""
    return {
        'username': 'testuser',
        'email': '<EMAIL>',
        'full_name': 'Test User',
        'role': UserRole.USER
    }


@pytest.fixture
def sample_category_data():
    """Sample category data for testing"""
    return {
        'name': 'Test Category',
        'description': 'Test category description',
        'color': '#0078D4'
    }


@pytest.fixture
def sample_document_data():
    """Sample document data for testing"""
    return {
        'title': 'Test Document',
        'description': 'Test document description',
        'file_name': 'test.pdf',
        'file_path': '/path/to/test.pdf',
        'file_size': 1024000,  # 1MB
        'file_type': 'pdf',
        'mime_type': 'application/pdf',
        'created_by': 1
    }


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
