"""
Complete Main Window for Electronic Archive System
النافذة الرئيسية المتكاملة لنظام الأرشفة الإلكترونية

Main application window with all integrated features
"""

import logging
from typing import Optional, Dict, Any, List
from pathlib import Path

from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QSplitter,
    QTreeWidget, QTreeWidgetItem, QTableWidget, QTableWidgetItem,
    QMenuBar, QMenu, QToolBar, QStatusBar, QLabel, QPushButton,
    QLineEdit, QComboBox, QMessageBox, QFileDialog, QProgressBar,
    QTabWidget, QTextEdit, QGroupBox, QGridLayout, QHeaderView,
    QFrame, QScrollArea, QCheckBox, QDateEdit, QSpinBox
)
from PySide6.QtCore import Qt, QTimer, Signal, QThread, QDate, QSettings
from PySide6.QtGui import QAction, QIcon, QPixmap, QFont, QKeySequence

from ..core.config import Config
from ..core.database_sqlite import DatabaseManagerSQLite
from ..models import User, Category, Document
from ..ui.theme_manager import theme_manager, ThemeType


class CompleteMainWindow(QMainWindow):
    """
    Complete main application window with all features
    النافذة الرئيسية المتكاملة مع جميع الميزات
    """
    
    def __init__(self, config: Config, db_manager: DatabaseManagerSQLite, services: Dict[str, Any]):
        """
        Initialize complete main window
        
        Args:
            config: Configuration manager
            db_manager: Database manager
            services: Dictionary of available services
        """
        super().__init__()
        
        self.config = config
        self.db_manager = db_manager
        self.services = services
        self.current_user: Optional[User] = None
        self.logger = logging.getLogger(__name__)
        
        # UI state
        self.current_documents = []
        self.selected_document = None
        
        self.setup_ui()
        self.setup_connections()
        self.load_initial_data()
        
        # Set window properties
        self.setWindowTitle("نظام الأرشفة الإلكترونية - Electronic Archive System")
        self.setMinimumSize(1200, 800)
        
        # Load window geometry
        self.load_window_settings()
        
        # Center window on screen
        self.center_on_screen()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم المتكاملة"""
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        
        # Create toolbar area
        self.setup_toolbar_area(main_layout)
        
        # Create main content area
        self.setup_main_content(main_layout)
        
        # Setup menu bar
        self.setup_menu_bar()
        
        # Setup status bar
        self.setup_status_bar()
    
    def setup_toolbar_area(self, parent_layout):
        """إعداد منطقة أشرطة الأدوات"""
        toolbar_frame = QFrame()
        toolbar_layout = QVBoxLayout(toolbar_frame)
        toolbar_layout.setContentsMargins(0, 0, 0, 0)
        
        # Main toolbar
        main_toolbar = QToolBar("الأدوات الرئيسية")
        main_toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        
        # Document actions
        self.add_doc_action = QAction("إضافة وثيقة", self)
        self.add_doc_action.setShortcut(QKeySequence("Ctrl+N"))
        main_toolbar.addAction(self.add_doc_action)
        
        self.scan_action = QAction("مسح ضوئي", self)
        self.scan_action.setShortcut(QKeySequence("Ctrl+S"))
        main_toolbar.addAction(self.scan_action)
        
        self.import_action = QAction("استيراد", self)
        self.import_action.setShortcut(QKeySequence("Ctrl+I"))
        main_toolbar.addAction(self.import_action)
        
        main_toolbar.addSeparator()
        
        # View actions
        self.refresh_action = QAction("تحديث", self)
        self.refresh_action.setShortcut(QKeySequence("F5"))
        main_toolbar.addAction(self.refresh_action)
        
        # Theme toggle
        self.theme_action = QAction("تبديل المظهر", self)
        self.theme_action.setShortcut(QKeySequence("Ctrl+T"))
        main_toolbar.addAction(self.theme_action)
        
        toolbar_layout.addWidget(main_toolbar)
        
        # Search toolbar
        search_toolbar = QToolBar("البحث")
        
        # Search input
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("البحث في الوثائق... (Ctrl+F)")
        self.search_input.setMinimumWidth(300)
        search_toolbar.addWidget(QLabel("البحث:"))
        search_toolbar.addWidget(self.search_input)
        
        # Search button
        self.search_button = QPushButton("بحث")
        search_toolbar.addWidget(self.search_button)
        
        # Advanced search button
        self.advanced_search_button = QPushButton("بحث متقدم")
        search_toolbar.addWidget(self.advanced_search_button)
        
        toolbar_layout.addWidget(search_toolbar)
        parent_layout.addWidget(toolbar_frame)
    
    def setup_main_content(self, parent_layout):
        """إعداد المحتوى الرئيسي"""
        # Create main splitter
        main_splitter = QSplitter(Qt.Horizontal)
        
        # Left panel - Navigation and filters
        self.setup_left_panel(main_splitter)
        
        # Center panel - Document list
        self.setup_center_panel(main_splitter)
        
        # Right panel - Document details and preview
        self.setup_right_panel(main_splitter)
        
        # Set splitter proportions
        main_splitter.setSizes([250, 600, 350])
        
        parent_layout.addWidget(main_splitter)
    
    def setup_left_panel(self, parent):
        """إعداد اللوحة اليسرى - التنقل والمرشحات"""
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        
        # Quick filters
        filters_group = QGroupBox("المرشحات السريعة")
        filters_layout = QVBoxLayout(filters_group)
        
        self.all_docs_btn = QPushButton("جميع الوثائق")
        self.recent_docs_btn = QPushButton("الوثائق الحديثة")
        self.my_docs_btn = QPushButton("وثائقي")
        self.favorites_btn = QPushButton("المفضلة")
        self.trash_btn = QPushButton("سلة المهملات")
        
        for btn in [self.all_docs_btn, self.recent_docs_btn, self.my_docs_btn, 
                   self.favorites_btn, self.trash_btn]:
            filters_layout.addWidget(btn)
        
        left_layout.addWidget(filters_group)
        
        # Categories tree
        categories_group = QGroupBox("الفئات")
        categories_layout = QVBoxLayout(categories_group)
        
        self.categories_tree = QTreeWidget()
        self.categories_tree.setHeaderLabel("الفئات")
        self.categories_tree.setRightToLeft(True)
        categories_layout.addWidget(self.categories_tree)
        
        # Category management buttons
        cat_buttons_layout = QHBoxLayout()
        self.add_category_btn = QPushButton("إضافة")
        self.edit_category_btn = QPushButton("تعديل")
        self.delete_category_btn = QPushButton("حذف")
        
        cat_buttons_layout.addWidget(self.add_category_btn)
        cat_buttons_layout.addWidget(self.edit_category_btn)
        cat_buttons_layout.addWidget(self.delete_category_btn)
        
        categories_layout.addLayout(cat_buttons_layout)
        left_layout.addWidget(categories_group)
        
        # Advanced filters
        advanced_group = QGroupBox("مرشحات متقدمة")
        advanced_layout = QGridLayout(advanced_group)
        
        # File type filter
        advanced_layout.addWidget(QLabel("نوع الملف:"), 0, 0)
        self.file_type_combo = QComboBox()
        self.file_type_combo.addItems(["الكل", "PDF", "صورة", "وورد", "إكسل", "أخرى"])
        advanced_layout.addWidget(self.file_type_combo, 0, 1)
        
        # Date range filter
        advanced_layout.addWidget(QLabel("من تاريخ:"), 1, 0)
        self.date_from = QDateEdit()
        self.date_from.setDate(QDate.currentDate().addDays(-30))
        advanced_layout.addWidget(self.date_from, 1, 1)
        
        advanced_layout.addWidget(QLabel("إلى تاريخ:"), 2, 0)
        self.date_to = QDateEdit()
        self.date_to.setDate(QDate.currentDate())
        advanced_layout.addWidget(self.date_to, 2, 1)
        
        # Size filter
        advanced_layout.addWidget(QLabel("الحجم (MB):"), 3, 0)
        size_layout = QHBoxLayout()
        self.size_from = QSpinBox()
        self.size_from.setMaximum(1000)
        self.size_to = QSpinBox()
        self.size_to.setMaximum(1000)
        self.size_to.setValue(100)
        size_layout.addWidget(self.size_from)
        size_layout.addWidget(QLabel("-"))
        size_layout.addWidget(self.size_to)
        advanced_layout.addLayout(size_layout, 3, 1)
        
        # Apply filters button
        self.apply_filters_btn = QPushButton("تطبيق المرشحات")
        advanced_layout.addWidget(self.apply_filters_btn, 4, 0, 1, 2)
        
        left_layout.addWidget(advanced_group)
        
        # Add stretch
        left_layout.addStretch()
        
        parent.addWidget(left_widget)
    
    def setup_center_panel(self, parent):
        """إعداد اللوحة الوسطى - قائمة الوثائق"""
        center_widget = QWidget()
        center_layout = QVBoxLayout(center_widget)
        
        # Documents header
        header_layout = QHBoxLayout()
        
        self.docs_count_label = QLabel("0 وثيقة")
        header_layout.addWidget(self.docs_count_label)
        
        header_layout.addStretch()
        
        # View options
        self.view_combo = QComboBox()
        self.view_combo.addItems(["عرض قائمة", "عرض شبكي", "عرض تفصيلي"])
        header_layout.addWidget(QLabel("العرض:"))
        header_layout.addWidget(self.view_combo)
        
        # Sort options
        self.sort_combo = QComboBox()
        self.sort_combo.addItems(["الأحدث أولاً", "الأقدم أولاً", "الاسم أ-ي", "الاسم ي-أ", "الحجم"])
        header_layout.addWidget(QLabel("الترتيب:"))
        header_layout.addWidget(self.sort_combo)
        
        center_layout.addLayout(header_layout)
        
        # Documents table
        self.documents_table = QTableWidget()
        self.setup_documents_table()
        center_layout.addWidget(self.documents_table)
        
        # Documents actions
        actions_layout = QHBoxLayout()
        
        self.edit_doc_btn = QPushButton("تعديل")
        self.delete_doc_btn = QPushButton("حذف")
        self.export_doc_btn = QPushButton("تصدير")
        self.print_doc_btn = QPushButton("طباعة")
        
        actions_layout.addWidget(self.edit_doc_btn)
        actions_layout.addWidget(self.delete_doc_btn)
        actions_layout.addWidget(self.export_doc_btn)
        actions_layout.addWidget(self.print_doc_btn)
        actions_layout.addStretch()
        
        center_layout.addLayout(actions_layout)
        
        parent.addWidget(center_widget)
    
    def setup_right_panel(self, parent):
        """إعداد اللوحة اليمنى - تفاصيل الوثيقة والمعاينة"""
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        
        # Document details tabs
        self.details_tabs = QTabWidget()
        
        # Preview tab
        preview_tab = QWidget()
        preview_layout = QVBoxLayout(preview_tab)
        
        self.preview_label = QLabel("اختر وثيقة لعرض المعاينة")
        self.preview_label.setAlignment(Qt.AlignCenter)
        self.preview_label.setStyleSheet("""
            QLabel {
                border: 2px dashed #cccccc;
                border-radius: 8px;
                background-color: #f9f9f9;
                color: #666666;
                font-size: 14px;
                padding: 20px;
            }
        """)
        self.preview_label.setMinimumHeight(200)
        
        preview_layout.addWidget(self.preview_label)
        
        # Preview controls
        preview_controls = QHBoxLayout()
        self.zoom_in_btn = QPushButton("تكبير")
        self.zoom_out_btn = QPushButton("تصغير")
        self.zoom_fit_btn = QPushButton("ملائمة")
        
        preview_controls.addWidget(self.zoom_in_btn)
        preview_controls.addWidget(self.zoom_out_btn)
        preview_controls.addWidget(self.zoom_fit_btn)
        preview_controls.addStretch()
        
        preview_layout.addLayout(preview_controls)
        
        self.details_tabs.addTab(preview_tab, "المعاينة")
        
        # Properties tab
        properties_tab = QWidget()
        properties_layout = QVBoxLayout(properties_tab)
        
        # Create scrollable properties area
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QGridLayout(scroll_widget)
        
        # Document properties
        row = 0
        
        scroll_layout.addWidget(QLabel("العنوان:"), row, 0)
        self.title_label = QLabel("-")
        self.title_label.setWordWrap(True)
        scroll_layout.addWidget(self.title_label, row, 1)
        row += 1
        
        scroll_layout.addWidget(QLabel("الوصف:"), row, 0)
        self.description_text = QTextEdit()
        self.description_text.setMaximumHeight(80)
        self.description_text.setReadOnly(True)
        scroll_layout.addWidget(self.description_text, row, 1)
        row += 1
        
        scroll_layout.addWidget(QLabel("الفئة:"), row, 0)
        self.category_label = QLabel("-")
        scroll_layout.addWidget(self.category_label, row, 1)
        row += 1
        
        scroll_layout.addWidget(QLabel("نوع الملف:"), row, 0)
        self.type_label = QLabel("-")
        scroll_layout.addWidget(self.type_label, row, 1)
        row += 1
        
        scroll_layout.addWidget(QLabel("حجم الملف:"), row, 0)
        self.size_label = QLabel("-")
        scroll_layout.addWidget(self.size_label, row, 1)
        row += 1
        
        scroll_layout.addWidget(QLabel("تاريخ الإنشاء:"), row, 0)
        self.created_date_label = QLabel("-")
        scroll_layout.addWidget(self.created_date_label, row, 1)
        row += 1
        
        scroll_layout.addWidget(QLabel("تاريخ الوثيقة:"), row, 0)
        self.document_date_label = QLabel("-")
        scroll_layout.addWidget(self.document_date_label, row, 1)
        row += 1
        
        scroll_layout.addWidget(QLabel("المنشئ:"), row, 0)
        self.creator_label = QLabel("-")
        scroll_layout.addWidget(self.creator_label, row, 1)
        row += 1
        
        scroll_layout.addWidget(QLabel("العلامات:"), row, 0)
        self.tags_label = QLabel("-")
        self.tags_label.setWordWrap(True)
        scroll_layout.addWidget(self.tags_label, row, 1)
        row += 1
        
        scroll_layout.setRowStretch(row, 1)  # Add stretch
        
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        properties_layout.addWidget(scroll_area)
        
        self.details_tabs.addTab(properties_tab, "الخصائص")
        
        right_layout.addWidget(self.details_tabs)
        
        parent.addWidget(right_widget)
    
    def setup_documents_table(self):
        """إعداد جدول الوثائق"""
        headers = ["العنوان", "النوع", "الحجم", "الفئة", "تاريخ الإنشاء", "المنشئ"]
        self.documents_table.setColumnCount(len(headers))
        self.documents_table.setHorizontalHeaderLabels(headers)
        
        # Set table properties
        self.documents_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.documents_table.setAlternatingRowColors(True)
        self.documents_table.setSortingEnabled(True)
        
        # Set column widths
        header = self.documents_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # Title
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Type
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Size
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Category
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Date
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Creator
    
    def setup_menu_bar(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu("ملف")
        
        file_menu.addAction(self.add_doc_action)
        file_menu.addAction(self.scan_action)
        file_menu.addAction(self.import_action)
        file_menu.addSeparator()
        
        exit_action = QAction("خروج", self)
        exit_action.setShortcut(QKeySequence("Ctrl+Q"))
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Edit menu
        edit_menu = menubar.addMenu("تحرير")
        
        search_action = QAction("بحث", self)
        search_action.setShortcut(QKeySequence("Ctrl+F"))
        search_action.triggered.connect(self.focus_search)
        edit_menu.addAction(search_action)
        
        # View menu
        view_menu = menubar.addMenu("عرض")
        
        view_menu.addAction(self.refresh_action)
        view_menu.addAction(self.theme_action)
        
        # Tools menu
        tools_menu = menubar.addMenu("أدوات")
        
        # Help menu
        help_menu = menubar.addMenu("مساعدة")
        
        about_action = QAction("حول البرنامج", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_status_bar(self):
        """إعداد شريط الحالة"""
        self.status_bar = self.statusBar()
        
        # Status label
        self.status_label = QLabel("جاهز")
        self.status_bar.addWidget(self.status_label)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.status_bar.addPermanentWidget(self.progress_bar)
        
        # User info
        self.user_label = QLabel("غير مسجل الدخول")
        self.status_bar.addPermanentWidget(self.user_label)
        
        # Theme indicator
        self.theme_label = QLabel("مظهر فاتح")
        self.status_bar.addPermanentWidget(self.theme_label)
    
    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        # Search
        self.search_input.returnPressed.connect(self.perform_search)
        self.search_button.clicked.connect(self.perform_search)
        self.advanced_search_button.clicked.connect(self.show_advanced_search)
        
        # Quick filters
        self.all_docs_btn.clicked.connect(lambda: self.load_documents("all"))
        self.recent_docs_btn.clicked.connect(lambda: self.load_documents("recent"))
        self.my_docs_btn.clicked.connect(lambda: self.load_documents("my"))
        self.favorites_btn.clicked.connect(lambda: self.load_documents("favorites"))
        self.trash_btn.clicked.connect(lambda: self.load_documents("trash"))
        
        # Categories
        self.categories_tree.itemSelectionChanged.connect(self.on_category_selected)
        self.add_category_btn.clicked.connect(self.add_category)
        self.edit_category_btn.clicked.connect(self.edit_category)
        self.delete_category_btn.clicked.connect(self.delete_category)
        
        # Documents
        self.documents_table.itemSelectionChanged.connect(self.on_document_selected)
        self.documents_table.itemDoubleClicked.connect(self.open_document)
        
        # Document actions
        self.add_doc_action.triggered.connect(self.add_document)
        self.scan_action.triggered.connect(self.scan_document)
        self.import_action.triggered.connect(self.import_documents)
        self.refresh_action.triggered.connect(self.refresh_view)
        self.theme_action.triggered.connect(self.toggle_theme)
        
        self.edit_doc_btn.clicked.connect(self.edit_document)
        self.delete_doc_btn.clicked.connect(self.delete_document)
        self.export_doc_btn.clicked.connect(self.export_document)
        self.print_doc_btn.clicked.connect(self.print_document)
        
        # Filters
        self.apply_filters_btn.clicked.connect(self.apply_advanced_filters)
        self.file_type_combo.currentTextChanged.connect(self.apply_quick_filters)
        
        # View options
        self.view_combo.currentTextChanged.connect(self.change_view_mode)
        self.sort_combo.currentTextChanged.connect(self.change_sort_order)
        
        # Preview controls
        self.zoom_in_btn.clicked.connect(self.zoom_in_preview)
        self.zoom_out_btn.clicked.connect(self.zoom_out_preview)
        self.zoom_fit_btn.clicked.connect(self.zoom_fit_preview)
        
        # Theme manager connection
        theme_manager.theme_changed.connect(self.on_theme_changed)
    
    def set_current_user(self, user: User):
        """Set current user"""
        self.current_user = user
        self.user_label.setText(f"مرحباً، {user.full_name or user.username}")
        self.logger.info(f"تم تسجيل دخول المستخدم: {user.username}")
    
    def center_on_screen(self):
        """توسيط النافذة على الشاشة"""
        screen = self.screen().availableGeometry()
        window = self.frameGeometry()
        window.moveCenter(screen.center())
        self.move(window.topLeft())
    
    def load_window_settings(self):
        """تحميل إعدادات النافذة"""
        settings = QSettings()
        geometry = settings.value("geometry")
        if geometry:
            self.restoreGeometry(geometry)
        
        window_state = settings.value("windowState")
        if window_state:
            self.restoreState(window_state)
    
    def save_window_settings(self):
        """حفظ إعدادات النافذة"""
        settings = QSettings()
        settings.setValue("geometry", self.saveGeometry())
        settings.setValue("windowState", self.saveState())
    
    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        self.load_categories()
        self.load_documents("all")
    
    def load_categories(self):
        """تحميل الفئات في الشجرة"""
        try:
            self.categories_tree.clear()
            
            with self.db_manager.get_session() as session:
                # Get root categories
                root_categories = session.query(Category).filter(
                    Category.parent_id.is_(None),
                    Category.is_active == True
                ).order_by(Category.name).all()
                
                for category in root_categories:
                    self._add_category_to_tree(category, None)
            
            self.categories_tree.expandAll()
            
        except Exception as e:
            self.logger.error(f"خطأ في تحميل الفئات: {e}")
    
    def _add_category_to_tree(self, category: Category, parent_item):
        """إضافة فئة إلى الشجرة"""
        if parent_item:
            item = QTreeWidgetItem(parent_item)
        else:
            item = QTreeWidgetItem(self.categories_tree)
        
        item.setText(0, category.name)
        item.setData(0, Qt.UserRole, category.id)
        
        # Add children
        for child in category.children:
            if child.is_active:
                self._add_category_to_tree(child, item)
    
    def load_documents(self, filter_type: str = "all"):
        """تحميل الوثائق حسب المرشح"""
        try:
            self.documents_table.setRowCount(0)
            self.current_documents = []
            
            with self.db_manager.get_session() as session:
                query = session.query(Document).filter(Document.is_active == True)
                
                if filter_type == "recent":
                    query = query.order_by(Document.created_at.desc()).limit(50)
                elif filter_type == "my" and self.current_user:
                    query = query.filter(Document.created_by == self.current_user.id)
                elif filter_type == "trash":
                    query = session.query(Document).filter(Document.is_active == False)
                
                documents = query.all()
                self.current_documents = documents
                
                # Populate table
                self.documents_table.setRowCount(len(documents))
                
                for row, doc in enumerate(documents):
                    self.documents_table.setItem(row, 0, QTableWidgetItem(doc.title))
                    self.documents_table.setItem(row, 1, QTableWidgetItem(doc.file_type.upper()))
                    self.documents_table.setItem(row, 2, QTableWidgetItem(self.format_file_size(doc.file_size)))
                    
                    # Category name
                    category_name = doc.category.name if doc.category else "غير محدد"
                    self.documents_table.setItem(row, 3, QTableWidgetItem(category_name))
                    
                    # Created date
                    created_date = doc.created_at.strftime("%Y-%m-%d %H:%M")
                    self.documents_table.setItem(row, 4, QTableWidgetItem(created_date))
                    
                    # Creator
                    creator_name = doc.creator.full_name if doc.creator else "غير معروف"
                    self.documents_table.setItem(row, 5, QTableWidgetItem(creator_name))
                    
                    # Store document ID in first column
                    self.documents_table.item(row, 0).setData(Qt.UserRole, doc.id)
                
                # Update count
                self.docs_count_label.setText(f"{len(documents)} وثيقة")
                
        except Exception as e:
            self.logger.error(f"خطأ في تحميل الوثائق: {e}")
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل الوثائق: {e}")
    
    def format_file_size(self, size_bytes: int) -> str:
        """تنسيق حجم الملف"""
        if size_bytes == 0:
            return "0 بايت"
        
        size_names = ["بايت", "كيلوبايت", "ميجابايت", "جيجابايت"]
        i = 0
        size = float(size_bytes)
        
        while size >= 1024 and i < len(size_names) - 1:
            size /= 1024.0
            i += 1
        
        return f"{size:.1f} {size_names[i]}"
    
    # Event handlers (to be implemented)
    def perform_search(self): pass
    def show_advanced_search(self): pass
    def on_category_selected(self): pass
    def on_document_selected(self): pass
    def open_document(self): pass
    def add_document(self): pass
    def scan_document(self): pass
    def import_documents(self): pass
    def refresh_view(self): pass
    def toggle_theme(self): pass
    def add_category(self): pass
    def edit_category(self): pass
    def delete_category(self): pass
    def edit_document(self): pass
    def delete_document(self): pass
    def export_document(self): pass
    def print_document(self): pass
    def apply_advanced_filters(self): pass
    def apply_quick_filters(self): pass
    def change_view_mode(self): pass
    def change_sort_order(self): pass
    def zoom_in_preview(self): pass
    def zoom_out_preview(self): pass
    def zoom_fit_preview(self): pass
    def focus_search(self): pass
    def on_theme_changed(self, theme_name: str): pass
    
    def show_about(self):
        """عرض معلومات البرنامج"""
        QMessageBox.about(
            self,
            "حول البرنامج",
            """
نظام الأرشفة الإلكترونية المتكامل
Complete Electronic Archive System

الإصدار: 1.0.0
Version: 1.0.0

نظام شامل لإدارة الوثائق والأرشفة الإلكترونية مع:
• إدارة الوثائق الشاملة
• بحث نصي كامل مع FTS5
• معاينة وصور مصغرة
• مسح ضوئي مدمج
• تصدير وطباعة
• سمات فاتحة وداكنة

© 2025 Archive Solutions
            """
        )
    
    def closeEvent(self, event):
        """Handle close event"""
        self.save_window_settings()
        event.accept()
