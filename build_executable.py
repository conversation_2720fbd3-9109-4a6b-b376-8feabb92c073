#!/usr/bin/env python3
"""
Build Executable for Electronic Archive System
بناء ملف تنفيذي لنظام الأرشفة الإلكترونية

Creates standalone executable using PyInstaller
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path


def build_executable():
    """Build standalone executable"""
    print("=" * 60)
    print("بناء ملف تنفيذي لنظام الأرشفة الإلكترونية")
    print("Building Electronic Archive System Executable")
    print("=" * 60)
    
    # Check if PyInstaller is installed
    try:
        import PyInstaller
        print(f"✅ PyInstaller version: {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller not found. Installing...")
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"])
    
    # Project paths
    project_root = Path(__file__).parent
    dist_dir = project_root / "dist"
    build_dir = project_root / "build"
    
    # Clean previous builds
    if dist_dir.exists():
        shutil.rmtree(dist_dir)
    if build_dir.exists():
        shutil.rmtree(build_dir)
    
    print("\n🔧 Building executable...")
    
    # PyInstaller command
    pyinstaller_args = [
        "pyinstaller",
        "--onefile",  # Single executable file
        "--windowed",  # No console window (for GUI)
        "--name", "ArchiveSystem",
        "--icon", "resources/icons/app.ico" if Path("resources/icons/app.ico").exists() else None,
        
        # Add data files
        "--add-data", "src;src",
        "--add-data", "resources;resources" if Path("resources").exists() else None,
        
        # Hidden imports
        "--hidden-import", "PySide6.QtCore",
        "--hidden-import", "PySide6.QtWidgets",
        "--hidden-import", "PySide6.QtGui",
        "--hidden-import", "PySide6.QtPrintSupport",
        "--hidden-import", "sqlalchemy",
        "--hidden-import", "bcrypt",
        "--hidden-import", "PIL",
        "--hidden-import", "comtypes" if sys.platform == "win32" else None,
        
        # Exclude unnecessary modules
        "--exclude-module", "tkinter",
        "--exclude-module", "matplotlib",
        "--exclude-module", "numpy",
        
        # Main script
        "advanced_app.py"
    ]
    
    # Remove None values
    pyinstaller_args = [arg for arg in pyinstaller_args if arg is not None]
    
    try:
        # Run PyInstaller
        result = subprocess.run(pyinstaller_args, check=True, capture_output=True, text=True)
        print("✅ Build successful!")
        
        # Copy additional files
        print("\n📁 Copying additional files...")
        
        executable_dir = dist_dir / "ArchiveSystem"
        if not executable_dir.exists():
            executable_dir = dist_dir
        
        # Copy config template
        config_template = project_root / "config.ini"
        if config_template.exists():
            shutil.copy2(config_template, executable_dir / "config.ini")
            print("✅ Copied config.ini")
        
        # Copy README
        readme_file = project_root / "README.md"
        if readme_file.exists():
            shutil.copy2(readme_file, executable_dir / "README.md")
            print("✅ Copied README.md")
        
        # Copy license
        license_file = project_root / "LICENSE"
        if license_file.exists():
            shutil.copy2(license_file, executable_dir / "LICENSE")
            print("✅ Copied LICENSE")
        
        # Create directories
        for dir_name in ["documents", "thumbnails", "temp", "backup", "logs"]:
            dir_path = executable_dir / dir_name
            dir_path.mkdir(exist_ok=True)
            print(f"✅ Created directory: {dir_name}")
        
        print(f"\n🎉 Build completed successfully!")
        print(f"📁 Executable location: {executable_dir}")
        
        # Show build info
        executable_file = executable_dir / "ArchiveSystem.exe" if sys.platform == "win32" else executable_dir / "ArchiveSystem"
        if executable_file.exists():
            file_size = executable_file.stat().st_size / (1024 * 1024)
            print(f"📊 Executable size: {file_size:.1f} MB")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Build failed: {e}")
        print(f"Error output: {e.stderr}")
        return False


def create_installer():
    """Create installer script"""
    print("\n🔧 Creating installer script...")
    
    installer_script = """
@echo off
echo ========================================
echo Electronic Archive System Installer
echo نظام الأرشفة الإلكترونية - المثبت
echo ========================================

echo.
echo Installing Electronic Archive System...
echo تثبيت نظام الأرشفة الإلكترونية...

REM Create installation directory
set INSTALL_DIR=%ProgramFiles%\\Electronic Archive System
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

REM Copy files
echo Copying files...
xcopy /E /I /Y . "%INSTALL_DIR%"

REM Create desktop shortcut
echo Creating desktop shortcut...
set DESKTOP=%USERPROFILE%\\Desktop
echo [InternetShortcut] > "%DESKTOP%\\Electronic Archive System.url"
echo URL=file:///%INSTALL_DIR%\\ArchiveSystem.exe >> "%DESKTOP%\\Electronic Archive System.url"
echo IconFile=%INSTALL_DIR%\\ArchiveSystem.exe >> "%DESKTOP%\\Electronic Archive System.url"
echo IconIndex=0 >> "%DESKTOP%\\Electronic Archive System.url"

REM Create start menu entry
set STARTMENU=%ProgramData%\\Microsoft\\Windows\\Start Menu\\Programs
if not exist "%STARTMENU%\\Electronic Archive System" mkdir "%STARTMENU%\\Electronic Archive System"
echo [InternetShortcut] > "%STARTMENU%\\Electronic Archive System\\Electronic Archive System.url"
echo URL=file:///%INSTALL_DIR%\\ArchiveSystem.exe >> "%STARTMENU%\\Electronic Archive System\\Electronic Archive System.url"
echo IconFile=%INSTALL_DIR%\\ArchiveSystem.exe >> "%STARTMENU%\\Electronic Archive System\\Electronic Archive System.url"
echo IconIndex=0 >> "%STARTMENU%\\Electronic Archive System\\Electronic Archive System.url"

echo.
echo Installation completed successfully!
echo تم التثبيت بنجاح!
echo.
echo You can now run Electronic Archive System from:
echo يمكنك الآن تشغيل نظام الأرشفة الإلكترونية من:
echo - Desktop shortcut
echo - Start Menu
echo - %INSTALL_DIR%\\ArchiveSystem.exe
echo.

pause
"""
    
    installer_path = Path("dist/install.bat")
    with open(installer_path, 'w', encoding='utf-8') as f:
        f.write(installer_script)
    
    print(f"✅ Created installer: {installer_path}")


def create_portable_version():
    """Create portable version"""
    print("\n📦 Creating portable version...")
    
    dist_dir = Path("dist")
    portable_dir = dist_dir / "ArchiveSystem_Portable"
    
    if portable_dir.exists():
        shutil.rmtree(portable_dir)
    
    # Copy executable and files
    shutil.copytree(dist_dir / "ArchiveSystem", portable_dir, ignore=shutil.ignore_patterns("*.spec"))
    
    # Create portable marker
    portable_marker = portable_dir / "portable.txt"
    with open(portable_marker, 'w') as f:
        f.write("This is a portable version of Electronic Archive System\n")
        f.write("All data will be stored in this directory.\n")
    
    # Create run script
    run_script = portable_dir / "run.bat"
    with open(run_script, 'w') as f:
        f.write("@echo off\n")
        f.write("cd /d %~dp0\n")
        f.write("ArchiveSystem.exe\n")
    
    print(f"✅ Created portable version: {portable_dir}")


def main():
    """Main function"""
    try:
        # Build executable
        if not build_executable():
            return 1
        
        # Create installer
        if sys.platform == "win32":
            create_installer()
        
        # Create portable version
        create_portable_version()
        
        print("\n" + "=" * 60)
        print("🎉 Build process completed successfully!")
        print("✅ Executable created")
        print("✅ Installer created (Windows)")
        print("✅ Portable version created")
        print("=" * 60)
        
        return 0
        
    except Exception as e:
        print(f"\n❌ Build process failed: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
"""
